<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 修仙游戏后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #2c3e50;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            border-radius: 5px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #34495e;
            color: #fff;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .badge-role {
            font-size: 0.8em;
            padding: 0.4em 0.8em;
        }
        .role-super_admin { background-color: #dc3545; }
        .role-admin { background-color: #fd7e14; }
        .role-operator { background-color: #20c997; }
        .role-viewer { background-color: #6c757d; }
        .status-active { color: #198754; }
        .status-inactive { color: #dc3545; }
        .status-suspended { color: #fd7e14; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">修仙后台</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item"><a class="nav-link" href="/"><i class="bi bi-speedometer2 me-2"></i>数据总览</a></li>
                        <li class="nav-item"><a class="nav-link" href="/dashboard"><i class="bi bi-graph-up me-2"></i>数据统计</a></li>
                        <li class="nav-item"><a class="nav-link" href="/players"><i class="bi bi-people me-2"></i>玩家管理</a></li>
                        <li class="nav-item"><a class="nav-link" href="/mails"><i class="bi bi-envelope me-2"></i>邮件管理</a></li>
                        <li class="nav-item"><a class="nav-link" href="/skills"><i class="bi bi-lightning me-2"></i>功法管理</a></li>
                        <li class="nav-item"><a class="nav-link" href="/sword-hearts"><i class="bi bi-heart me-2"></i>剑心管理</a></li>
                        <li class="nav-item"><a class="nav-link" href="/treasures"><i class="bi bi-gem me-2"></i>古宝管理</a></li>
                        <li class="nav-item"><a class="nav-link active" href="/admin-users"><i class="bi bi-person-gear me-2"></i>管理员管理</a></li>
                        <li class="nav-item"><a class="nav-link" href="/logs"><i class="bi bi-journal-text me-2"></i>操作日志</a></li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-person-gear me-2"></i><%= title %></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                            <i class="bi bi-plus-circle me-1"></i>添加管理员
                        </button>
                    </div>
                </div>

                <!-- 管理员列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list me-2"></i>管理员列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="adminTable">
                                <thead>
                                    <tr>
                                        <th>用户名</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>权限</th>
                                        <th>创建时间</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="adminTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加管理员模态框 -->
    <div class="modal fade" id="addAdminModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加管理员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAdminForm">
                        <div class="mb-3">
                            <label for="adminOpenid" class="form-label">微信OpenID</label>
                            <input type="text" class="form-control" id="adminOpenid" required>
                            <div class="form-text">管理员的微信OpenID，可通过游戏端获取</div>
                        </div>
                        <div class="mb-3">
                            <label for="adminUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="adminUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="adminRole" class="form-label">角色</label>
                            <select class="form-select" id="adminRole" required>
                                <option value="">请选择角色</option>
                                <option value="super_admin">超级管理员</option>
                                <option value="admin">普通管理员</option>
                                <option value="operator">运营人员</option>
                                <option value="viewer">只读用户</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="adminDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="adminDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addAdmin()">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑管理员模态框 -->
    <div class="modal fade" id="editAdminModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑管理员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editAdminForm">
                        <input type="hidden" id="editAdminId">
                        <div class="mb-3">
                            <label for="editAdminUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editAdminUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="editAdminRole" class="form-label">角色</label>
                            <select class="form-select" id="editAdminRole" required>
                                <option value="super_admin">超级管理员</option>
                                <option value="admin">普通管理员</option>
                                <option value="operator">运营人员</option>
                                <option value="viewer">只读用户</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editAdminStatus" class="form-label">状态</label>
                            <select class="form-select" id="editAdminStatus" required>
                                <option value="active">激活</option>
                                <option value="inactive">禁用</option>
                                <option value="suspended">暂停</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editAdminDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="editAdminDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateAdmin()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时获取管理员列表
        document.addEventListener('DOMContentLoaded', function() {
            loadAdminUsers();
        });

        // 加载管理员列表
        async function loadAdminUsers() {
            try {
                const response = await fetch('/api/admin-users');
                const result = await response.json();
                
                if (result.success) {
                    displayAdminUsers(result.data);
                } else {
                    alert('加载管理员列表失败: ' + result.error);
                }
            } catch (error) {
                console.error('加载管理员列表失败:', error);
                alert('加载管理员列表失败，请刷新页面重试');
            }
        }

        // 显示管理员列表
        function displayAdminUsers(admins) {
            const tbody = document.getElementById('adminTableBody');
            tbody.innerHTML = '';
            
            admins.forEach(admin => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${admin.username}</td>
                    <td><span class="badge badge-role role-${admin.role}">${getRoleDisplayName(admin.role)}</span></td>
                    <td><span class="status-${admin.status}">${getStatusDisplayName(admin.status)}</span></td>
                    <td>${getPermissionsDisplay(admin.permissions || [])}</td>
                    <td>${formatDate(admin.created_at)}</td>
                    <td>${admin.last_login ? formatDate(admin.last_login) : '从未登录'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editAdmin('${admin._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteAdmin('${admin._id}', '${admin.username}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取角色显示名称
        function getRoleDisplayName(role) {
            const roleNames = {
                'super_admin': '超级管理员',
                'admin': '普通管理员',
                'operator': '运营人员',
                'viewer': '只读用户'
            };
            return roleNames[role] || role;
        }

        // 获取状态显示名称
        function getStatusDisplayName(status) {
            const statusNames = {
                'active': '激活',
                'inactive': '禁用',
                'suspended': '暂停'
            };
            return statusNames[status] || status;
        }

        // 获取权限显示
        function getPermissionsDisplay(permissions) {
            if (permissions.includes('*')) {
                return '全部权限';
            }
            return permissions.join(', ') || '无权限';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 添加管理员
        async function addAdmin() {
            const openid = document.getElementById('adminOpenid').value;
            const username = document.getElementById('adminUsername').value;
            const role = document.getElementById('adminRole').value;
            const description = document.getElementById('adminDescription').value;
            
            if (!openid || !username || !role) {
                alert('请填写所有必填字段');
                return;
            }
            
            try {
                const response = await fetch('/api/admin-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        openid: openid,
                        username: username,
                        role: role,
                        status: 'active',
                        description: description
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('管理员添加成功');
                    bootstrap.Modal.getInstance(document.getElementById('addAdminModal')).hide();
                    document.getElementById('addAdminForm').reset();
                    loadAdminUsers();
                } else {
                    alert('添加失败: ' + result.error);
                }
            } catch (error) {
                console.error('添加管理员失败:', error);
                alert('添加失败，请重试');
            }
        }

        // 编辑管理员
        function editAdmin(adminId) {
            // 这里应该获取管理员详情并填充编辑表单
            // 为了简化，暂时跳过实现
            alert('编辑功能开发中...');
        }

        // 删除管理员
        async function deleteAdmin(adminId, username) {
            if (!confirm(`确定要删除管理员 "${username}" 吗？此操作不可恢复。`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/admin-users/${adminId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('管理员删除成功');
                    loadAdminUsers();
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除管理员失败:', error);
                alert('删除失败，请重试');
            }
        }
    </script>
</body>
</html>
