# 微信后台管理系统部署指南

## 🎯 概述

本指南将帮助您部署基于微信登录的后台管理系统，该系统支持通过微信openid进行管理员身份验证，并提供完整的数据管理功能。

## 📋 系统架构

```
微信小程序/小游戏 ←→ 微信云开发 ←→ adminService云函数 ←→ 后台管理系统
                                ↕
                           admin_users表
```

## 🚀 部署步骤

### 步骤1: 部署adminService云函数

1. **上传云函数代码**
   ```bash
   # 进入云函数目录
   cd cloudfunctions/adminService
   
   # 安装依赖
   npm install
   
   # 使用微信开发者工具或命令行工具部署
   # 方法1: 使用微信开发者工具
   # - 右键点击adminService文件夹
   # - 选择"上传并部署：云端安装依赖"
   
   # 方法2: 使用命令行工具
   tcb fn deploy adminService --env cloud1-9gzbxxbff827656f
   ```

2. **验证云函数部署**
   ```bash
   # 测试云函数是否正常工作
   tcb fn invoke adminService --env cloud1-9gzbxxbff827656f --params '{"action":"getServerTime"}'
   ```

### 步骤2: 创建admin_users数据表

1. **在微信云开发控制台创建表**
   - 登录微信云开发控制台
   - 进入数据库管理
   - 创建新集合：`admin_users`
   - 设置权限：仅管理员可读写

2. **表结构说明**
   ```javascript
   {
     "_id": "自动生成",
     "openid": "微信用户openid",
     "username": "管理员用户名",
     "role": "角色(super_admin/admin/operator/viewer)",
     "status": "状态(active/inactive/suspended)",
     "permissions": ["权限数组"],
     "description": "描述信息",
     "created_at": "创建时间",
     "last_login": "最后登录时间",
     "createdAt": "时间戳",
     "updatedAt": "更新时间戳"
   }
   ```

### 步骤3: 初始化管理员账号

1. **获取管理员openid**
   ```javascript
   // 在游戏端或小程序中运行以下代码获取openid
   wx.cloud.callFunction({
     name: 'getOpenId',
     success: res => {
       console.log('您的openid:', res.result.openid);
       // 将此openid用于创建管理员账号
     }
   });
   ```

2. **修改初始化脚本**
   ```javascript
   // 编辑 admin-system/scripts/init-admin-users.js
   const INITIAL_ADMINS = [
     {
       openid: 'your-actual-openid-here', // 替换为实际openid
       username: 'super_admin',
       role: 'super_admin',
       status: 'active',
       permissions: ['*'],
       created_at: new Date(),
       last_login: null,
       description: '超级管理员账号'
     }
   ];
   ```

3. **运行初始化脚本**
   ```bash
   cd admin-system
   node scripts/init-admin-users.js
   ```

### 步骤4: 配置后台管理系统

1. **安装依赖**
   ```bash
   cd admin-system
   npm install
   ```

2. **配置环境变量**
   ```bash
   # 创建.env文件
   cp .env.example .env
   
   # 编辑.env文件
   WECHAT_ENV_ID=cloud1-9gzbxxbff827656f
   PORT=3000
   NODE_ENV=production
   ```

3. **更新配置文件**
   ```javascript
   // 编辑 admin-system/config.js
   module.exports = {
     cloud: {
       envId: 'cloud1-9gzbxxbff827656f' // 确保与游戏端一致
     },
     // ... 其他配置
   };
   ```

### 步骤5: 启动后台管理系统

1. **启动服务**
   ```bash
   cd admin-system
   npm start
   ```

2. **访问系统**
   - 打开浏览器访问: http://localhost:3000
   - 系统会自动跳转到微信登录页面
   - 在微信中打开此页面进行登录

## 🔧 配置说明

### 权限角色配置

| 角色 | 权限 | 说明 |
|------|------|------|
| super_admin | * | 超级管理员，拥有所有权限 |
| admin | read, create, update, delete, manage_users | 普通管理员 |
| operator | read, create, update | 运营人员 |
| viewer | read | 只读用户 |

### 微信登录流程

1. 用户访问后台管理系统
2. 系统检查登录状态，未登录则跳转到微信登录页
3. 用户在微信中打开登录页面
4. 系统调用adminService云函数验证用户openid
5. 验证通过后设置session，跳转到管理界面

## 🧪 测试验证

1. **运行测试脚本**
   ```bash
   cd admin-system
   node test-wechat-admin.js
   ```

2. **手动测试步骤**
   - 访问 http://localhost:3000
   - 确认跳转到微信登录页面
   - 在微信中打开页面（或使用微信开发者工具）
   - 验证登录流程
   - 测试管理员功能

## 🔒 安全注意事项

1. **openid保护**
   - 不要在前端代码中硬编码openid
   - 定期轮换管理员账号
   - 监控异常登录行为

2. **权限控制**
   - 严格控制super_admin权限
   - 定期审查管理员权限
   - 记录所有管理操作日志

3. **网络安全**
   - 使用HTTPS部署生产环境
   - 配置防火墙规则
   - 启用访问日志

## 🐛 常见问题

### Q1: adminService云函数调用失败
**A:** 检查云函数是否正确部署，环境ID是否一致

### Q2: 微信登录时提示权限不足
**A:** 确认用户openid已添加到admin_users表中

### Q3: 后台管理系统无法启动
**A:** 检查Node.js版本（需要14+），确认依赖已正确安装

### Q4: 数据库连接失败
**A:** 验证云开发环境ID配置，检查网络连接

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看控制台错误日志
2. 运行测试脚本诊断问题
3. 检查云函数和数据库配置
4. 确认微信开发者工具配置正确

## 🔄 更新升级

### 更新云函数
```bash
cd cloudfunctions/adminService
npm update
# 重新部署云函数
```

### 更新后台系统
```bash
cd admin-system
git pull origin main
npm install
npm restart
```

## 📝 维护建议

1. **定期备份**
   - 备份admin_users表数据
   - 备份系统配置文件

2. **监控告警**
   - 监控云函数调用量
   - 设置异常登录告警

3. **日志管理**
   - 定期清理过期日志
   - 分析访问模式

---

**部署完成后，您将拥有一个功能完整的微信后台管理系统！** 🎉
