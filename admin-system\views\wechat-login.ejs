<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录 - 修仙游戏后台管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .login-body {
            padding: 2rem;
            text-align: center;
        }
        
        .wechat-login-btn {
            background: #07c160;
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-weight: 600;
            color: white;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .wechat-login-btn:hover {
            background: #06ad56;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(7, 193, 96, 0.3);
        }
        
        .wechat-login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-message {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 10px;
            display: none;
        }
        
        .status-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-message.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .login-footer {
            text-align: center;
            padding: 1rem;
            background-color: #f8f9fa;
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .version-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.75rem;
        }
        
        .loading-spinner {
            display: none;
            margin: 1rem 0;
        }
        
        .fallback-login {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }
        
        .fallback-login a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .fallback-login a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fab fa-weixin fa-3x mb-3"></i>
                <h1>微信登录</h1>
                <p>修仙游戏后台管理系统</p>
            </div>
            
            <div class="login-body">
                <!-- 状态消息 -->
                <div id="statusMessage" class="status-message">
                    <span id="statusText"></span>
                </div>
                
                <!-- 加载动画 -->
                <div id="loadingSpinner" class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在验证管理员权限...</p>
                </div>
                
                <!-- 微信登录按钮 -->
                <button id="wechatLoginBtn" class="wechat-login-btn" onclick="startWechatLogin()">
                    <i class="fab fa-weixin me-2"></i>
                    使用微信登录
                </button>
                
                <!-- 登录说明 -->
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        仅限已授权的管理员账号登录
                    </small>
                </div>
                
                <!-- 备用登录方式 -->
                <div class="fallback-login">
                    <small class="text-muted">
                        微信登录遇到问题？
                        <a href="/login">使用传统登录方式</a>
                    </small>
                </div>
            </div>
            
            <div class="login-footer">
                <i class="fas fa-shield-alt me-1"></i>
                微信安全登录 | 权限验证
            </div>
        </div>
    </div>
    
    <div class="version-info">
        v2.0.0 | 微信登录已启用
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 微信JS-SDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    
    <script>
        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessage');
            const statusText = document.getElementById('statusText');
            
            statusText.textContent = message;
            statusDiv.className = `status-message ${type}`;
            statusDiv.style.display = 'block';
            
            // 3秒后自动隐藏信息类消息
            if (type === 'info') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        // 显示/隐藏加载状态
        function setLoading(loading) {
            const spinner = document.getElementById('loadingSpinner');
            const button = document.getElementById('wechatLoginBtn');
            
            if (loading) {
                spinner.style.display = 'block';
                button.disabled = true;
            } else {
                spinner.style.display = 'none';
                button.disabled = false;
            }
        }
        
        // 开始微信登录流程
        async function startWechatLogin() {
            try {
                setLoading(true);
                showStatus('正在获取微信登录授权...', 'info');
                
                // 检查是否在微信环境中
                if (!isWechatBrowser()) {
                    showStatus('请在微信中打开此页面进行登录', 'error');
                    setLoading(false);
                    return;
                }
                
                // 调用微信登录
                await performWechatLogin();
                
            } catch (error) {
                console.error('微信登录失败:', error);
                showStatus('微信登录失败，请重试', 'error');
                setLoading(false);
            }
        }
        
        // 检查是否在微信浏览器中
        function isWechatBrowser() {
            const ua = navigator.userAgent.toLowerCase();
            return ua.includes('micromessenger');
        }
        
        // 执行微信登录
        async function performWechatLogin() {
            try {
                // 这里应该调用微信JS-SDK获取用户信息
                // 由于这是后台管理系统，我们需要通过云函数获取openid
                
                showStatus('正在验证管理员权限...', 'info');
                
                // 调用后台API验证管理员权限
                const response = await fetch('/api/auth/wechat-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'verify_admin'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('登录成功！正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showStatus(result.error || '权限验证失败', 'error');
                    setLoading(false);
                }
                
            } catch (error) {
                console.error('微信登录处理失败:', error);
                showStatus('登录处理失败，请重试', 'error');
                setLoading(false);
            }
        }
        
        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已经登录
            checkLoginStatus();
        });
        
        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/api/auth/check-status');
                const result = await response.json();
                
                if (result.success && result.isLoggedIn) {
                    showStatus('您已登录，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                }
            } catch (error) {
                console.log('登录状态检查失败:', error);
                // 忽略错误，继续显示登录页面
            }
        }
    </script>
</body>
</html>
