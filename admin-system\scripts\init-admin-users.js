/**
 * 初始化管理员用户表脚本
 * 用于在云数据库中创建admin_users表并添加初始管理员账号
 */

const { CloudFunctionAdapter } = require('../lib/CloudFunctionAdapter');

// 初始管理员配置
const INITIAL_ADMINS = [
  {
    openid: 'your-openid-here', // 需要替换为实际的openid
    username: 'super_admin',
    role: 'super_admin',
    status: 'active',
    permissions: ['*'],
    created_at: new Date(),
    last_login: null,
    description: '超级管理员账号'
  },
  {
    openid: 'another-openid-here', // 需要替换为实际的openid
    username: 'admin',
    role: 'admin',
    status: 'active',
    permissions: ['read', 'create', 'update', 'delete', 'manage_users'],
    created_at: new Date(),
    last_login: null,
    description: '普通管理员账号'
  }
];

async function initAdminUsers() {
  console.log('开始初始化管理员用户表...');
  
  try {
    const cloudAdapter = new CloudFunctionAdapter();
    
    // 检查admin_users表是否已有数据
    console.log('检查admin_users表现有数据...');
    const existingAdmins = await cloudAdapter.callFunction('adminService', {
      action: 'list',
      tableName: 'admin_users',
      limit: 1
    });
    
    if (existingAdmins.success && existingAdmins.data && existingAdmins.data.length > 0) {
      console.log('admin_users表已有数据，跳过初始化');
      console.log('现有管理员数量:', existingAdmins.data.length);
      return;
    }
    
    console.log('admin_users表为空，开始添加初始管理员...');
    
    // 添加初始管理员
    for (const admin of INITIAL_ADMINS) {
      console.log(`添加管理员: ${admin.username} (${admin.role})`);
      
      const result = await cloudAdapter.callFunction('adminService', {
        action: 'create',
        tableName: 'admin_users',
        data: admin
      });
      
      if (result.success) {
        console.log(`✓ 管理员 ${admin.username} 添加成功`);
      } else {
        console.error(`✗ 管理员 ${admin.username} 添加失败:`, result.error);
      }
    }
    
    console.log('管理员用户表初始化完成！');
    
    // 显示使用说明
    console.log('\n=== 重要提示 ===');
    console.log('1. 请将上述配置中的 openid 替换为实际的微信用户 openid');
    console.log('2. 可以通过游戏端获取管理员的 openid，或使用微信开发者工具');
    console.log('3. 初始化完成后，这些用户就可以通过微信登录后台管理系统');
    console.log('4. 可以在后台管理系统中动态添加、修改、删除管理员账号');
    
  } catch (error) {
    console.error('初始化管理员用户表失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initAdminUsers()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  initAdminUsers,
  INITIAL_ADMINS
};
