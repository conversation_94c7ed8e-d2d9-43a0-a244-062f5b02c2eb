const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const session = require('express-session');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入配置和中间件
let config;
try {
  // 优先尝试加载config.js
  config = require('./config.js');
  console.log('✅ 使用config.js配置文件');
} catch (error) {
  // 如果config.js不存在，使用config.example.js
  console.log('⚠️ config.js不存在，使用config.example.js');
  config = require('./config.example.js');
}

const CloudFunctionAdapter = require('./lib/CloudFunctionAdapter');
const { authenticateToken, requirePermission, optionalAuth } = require('./middleware/auth');
const { auditLogger } = require('./middleware/audit');

// 导入路由
const authRoutes = require('./routes/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// 信任代理设置（用于获取真实IP）
app.set('trust proxy', 1);

// Session配置
app.use(session({
  secret: process.env.SESSION_SECRET || 'xiuxian6-admin-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: { 
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24小时
  }
}));

// 全局速率限制
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 2000, // 每个IP最多2000个请求（增加限制）
  message: {
    success: false,
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // 跳过静态资源的速率限制
    return req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/);
  }
});

// 中间件配置
app.use(globalLimiter);
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 统一环境配置 - 使用游戏端相同的环境ID
const CLOUD_ENV_ID = config.cloud.envId; // cloud1-9gzbxxbff827656f

console.log('☁️ 使用云函数调用方式连接数据库');
console.log(`📊 数据库环境: ${CLOUD_ENV_ID}`);
console.log('🔒 认证和权限控制已启用');
console.log('📝 审计日志记录已启用');

// 初始化CloudFunctionAdapter
const cloudAdapter = new CloudFunctionAdapter({
  envId: CLOUD_ENV_ID,
  enableLogging: true,
  enablePerformanceMonitoring: true
});

// 数据表配置（使用config中的表名映射）
const tables = config.tables;

// 路由配置
app.use('/api/auth', authRoutes);

// 数据服务封装
const dataService = {
  async getStats() {
    try {
      const result = await cloudAdapter.getStats();
      return result;
    } catch (error) {
      console.error('获取统计数据失败:', error);
      throw error;
    }
  },

  async getList(tableName, query = {}, options = {}) {
    try {
      const result = await cloudAdapter.list(tableName, {
        conditions: query,
        ...options
      });
      return result;
    } catch (error) {
      console.error(`获取${tableName}列表失败:`, error);
      throw error;
    }
  },

  async create(tableName, data) {
    try {
      const result = await cloudAdapter.create(tableName, data);
      return result;
    } catch (error) {
      console.error(`创建${tableName}记录失败:`, error);
      throw error;
    }
  },

  async update(tableName, id, data) {
    try {
      const result = await cloudAdapter.update(tableName, data, { _id: id });
      return result;
    } catch (error) {
      console.error(`更新${tableName}记录失败:`, error);
      throw error;
    }
  },

  async delete(tableName, id) {
    try {
      const result = await cloudAdapter.delete(tableName, { _id: id });
      return result;
    } catch (error) {
      console.error(`删除${tableName}记录失败:`, error);
      throw error;
    }
  },

  async get(tableName, query) {
    try {
      const result = await cloudAdapter.get(tableName, query);
      return result;
    } catch (error) {
      console.error(`获取${tableName}记录失败:`, error);
      throw error;
    }
  },

  async count(tableName, query) {
    try {
      const result = await cloudAdapter.count(tableName, query);
      return result;
    } catch (error) {
      console.error(`获取${tableName}记录总数失败:`, error);
      throw error;
    }
  }
};

// 工具函数：记录管理员操作日志（已废弃，使用auditLogger代替）
async function logAdminAction(adminId, action, targetType, targetId, beforeData, afterData, description, req) {
  try {
    // 迁移到新的审计日志系统
    await auditLogger.recordAuditLog({
      admin_id: adminId,
      admin_username: req?.user?.username || 'system',
      action: action,
      target_type: targetType,
      target_id: targetId,
      before_data: beforeData,
      after_data: afterData,
      status: 'success',
      description: description,
      ip_address: req?.ip || req?.connection?.remoteAddress || 'unknown',
      user_agent: req?.get('User-Agent') || '',
      timestamp: new Date()
    });
    console.log(`📝 操作日志: ${description}`);
    return true;
  } catch (error) {
    console.error('记录操作日志失败:', error);
    return false;
  }
}

// 工具函数：处理数据库查询结果
function formatDbResult(result) {
  if (!result.data) return [];
  return result.data.map(item => ({
    ...item,
    id: item._id // 兼容前端的id字段
  }));
}

// ========== 前端页面路由 ==========

// 登录页面（公开访问）
app.get('/login', (req, res) => {
  res.render('login', { title: '管理员登录' });
});

// 微信登录页面（公开访问）
app.get('/wechat-login', (req, res) => {
  res.render('wechat-login', { title: '微信登录' });
});

// 首页（需要认证）
app.get('/', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      // 优先使用微信登录
      return res.redirect('/wechat-login');
    }
    
    // 获取统计数据（使用try-catch避免页面崩溃）
    let stats = {};
    try {
      stats = await dataService.getStats();
    } catch (error) {
      console.warn('获取首页统计数据失败:', error.message);
      stats = {
        players: 0,
        skill_templates: 0,
        sword_heart_templates: 0,
        treasure_templates: 0,
        gacha_pools: 0,
        mails: 0
      };
    }
    
    // 渲染首页
    res.render('index', {
      title: '修仙游戏后台管理',
      stats: stats,
      user: req.session.user || { username: 'admin', role: 'admin' }
    });
    
  } catch (error) {
    console.error('首页加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '系统暂时不可用，请稍后重试'
    });
  }
});

// 以下页面都需要认证
const authMiddleware = (req, res, next) => {
  // 对于HTML页面，检查是否有token（从query参数或session）
  const token = req.query.token || req.session?.token;
  if (token) {
    req.headers.authorization = `Bearer ${token}`;
  }
  
  // 使用可选认证中间件
  optionalAuth(req, res, () => {
    if (!req.user) {
      return res.redirect('/login');
    }
    next();
  });
};

// 玩家管理页面
app.get('/players', authMiddleware, (req, res) => {
  res.render('players', { 
    title: '玩家管理',
    user: req.user
  });
});

// 邮件管理页面
app.get('/mails', authMiddleware, (req, res) => {
  res.render('mails', { 
    title: '邮件管理',
    user: req.user
  });
});

// 物品模板管理页面
app.get('/templates', authMiddleware, (req, res) => {
  res.render('templates', { 
    title: '物品模板管理',
    user: req.user
  });
});

// 功法模板管理页面
app.get('/skills', authMiddleware, (req, res) => {
  res.render('skills', { 
    title: '功法模板管理',
    user: req.user
  });
});

// 剑心模板管理页面
app.get('/sword-hearts', authMiddleware, (req, res) => {
  res.render('sword-hearts', { 
    title: '剑心模板管理',
    user: req.user
  });
});

// 古宝模板管理页面
app.get('/treasures', authMiddleware, (req, res) => {
  res.render('treasures', { 
    title: '古宝模板管理',
    user: req.user
  });
});

// 抽取池管理页面
app.get('/gacha-pools', authMiddleware, (req, res) => {
  res.render('gacha-pools', { 
    title: '抽取池管理',
    user: req.user
  });
});

// 玩家资源管理页面（需要认证）
app.get('/player-resources', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.redirect('/login');
    }

    res.render('player-resources', {
      title: '玩家资源管理',
      user: req.session.user || { username: 'admin', role: 'admin' }
    });
    
  } catch (error) {
    console.error('玩家资源管理页面加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '页面加载失败，请稍后重试'
    });
  }
});

// 玩家装备和技能管理页面（需要认证）
app.get('/player-equipment', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.redirect('/login');
    }

    res.render('player-equipment', {
      title: '玩家装备和技能管理',
      user: req.session.user || { username: 'admin', role: 'admin' }
    });
    
  } catch (error) {
    console.error('玩家装备和技能管理页面加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '页面加载失败，请稍后重试'
    });
  }
});

// 数据统计面板页面（需要认证）
app.get('/dashboard', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.redirect('/login');
    }

    res.render('dashboard', {
      title: '数据统计面板',
      user: req.session.user || { username: 'admin', role: 'admin' }
    });
    
  } catch (error) {
    console.error('数据统计面板页面加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '页面加载失败，请稍后重试'
    });
  }
});

// 管理员管理页面（需要认证）
app.get('/admin-users', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.redirect('/wechat-login');
    }

    // 检查是否有管理员权限
    if (!req.session.user || !req.session.user.permissions ||
        (!req.session.user.permissions.includes('*') && !req.session.user.permissions.includes('manage_users'))) {
      return res.status(403).render('error', {
        title: '权限不足',
        message: '您没有权限访问管理员管理页面'
      });
    }

    res.render('admin-users', {
      title: '管理员管理',
      user: req.session.user
    });

  } catch (error) {
    console.error('管理员管理页面加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '页面加载失败，请稍后重试'
    });
  }
});

// 系统日志管理页面（需要认证）
app.get('/logs', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.redirect('/login');
    }

    res.render('logs', {
      title: '系统监控与日志管理',
      user: req.session.user || { username: 'admin', role: 'admin' }
    });
    
  } catch (error) {
    console.error('系统日志管理页面加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '页面加载失败，请稍后重试'
    });
  }
});

// 批量操作页面（需要认证）
app.get('/batch-operations', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.redirect('/login');
    }

    res.render('batch-operations', {
      title: '批量操作和数据管理工具',
      user: req.session.user || { username: 'admin', role: 'admin' }
    });
    
  } catch (error) {
    console.error('批量操作页面加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '页面加载失败，请稍后重试'
    });
  }
});

// ========== API路由 ==========

// ========== 管理员管理API ==========

// 获取管理员列表
app.get('/api/admin-users', async (req, res) => {
  try {
    // 检查登录状态
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    // 检查权限
    if (!req.session.user || !req.session.user.permissions ||
        (!req.session.user.permissions.includes('*') && !req.session.user.permissions.includes('manage_users'))) {
      return res.status(403).json({
        success: false,
        error: '权限不足',
        code: 'PERMISSION_DENIED'
      });
    }

    // 调用adminService获取管理员列表
    const result = await dataService.callCloudFunction('adminService', {
      action: 'list',
      tableName: 'admin_users',
      limit: 100
    });

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        total: result.data.length
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '获取管理员列表失败',
        code: 'GET_ADMIN_USERS_FAILED'
      });
    }

  } catch (error) {
    console.error('获取管理员列表API错误:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    });
  }
});

// 添加管理员
app.post('/api/admin-users', async (req, res) => {
  try {
    // 检查登录状态
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    // 检查权限
    if (!req.session.user || !req.session.user.permissions ||
        (!req.session.user.permissions.includes('*') && !req.session.user.permissions.includes('manage_users'))) {
      return res.status(403).json({
        success: false,
        error: '权限不足',
        code: 'PERMISSION_DENIED'
      });
    }

    const { openid, username, role, status = 'active', description = '' } = req.body;

    if (!openid || !username || !role) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数',
        code: 'MISSING_PARAMS'
      });
    }

    // 调用adminService添加管理员
    const result = await dataService.callCloudFunction('adminService', {
      action: 'create',
      tableName: 'admin_users',
      data: {
        openid: openid,
        username: username,
        role: role,
        status: status,
        description: description,
        created_at: new Date(),
        last_login: null
      }
    });

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: '管理员添加成功'
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '添加管理员失败',
        code: 'ADD_ADMIN_FAILED'
      });
    }

  } catch (error) {
    console.error('添加管理员API错误:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    });
  }
});

// 删除管理员
app.delete('/api/admin-users/:id', async (req, res) => {
  try {
    // 检查登录状态
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    // 检查权限
    if (!req.session.user || !req.session.user.permissions ||
        (!req.session.user.permissions.includes('*') && !req.session.user.permissions.includes('manage_users'))) {
      return res.status(403).json({
        success: false,
        error: '权限不足',
        code: 'PERMISSION_DENIED'
      });
    }

    const adminId = req.params.id;

    // 调用adminService删除管理员
    const result = await dataService.callCloudFunction('adminService', {
      action: 'delete',
      tableName: 'admin_users',
      _id: adminId
    });

    if (result.success) {
      res.json({
        success: true,
        message: '管理员删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '删除管理员失败',
        code: 'DELETE_ADMIN_FAILED'
      });
    }

  } catch (error) {
    console.error('删除管理员API错误:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    });
  }
});

// ========== 统计数据API ==========
app.get('/api/stats', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    // 获取统计数据
    let stats = {};
    try {
      stats = await dataService.getStats();
    } catch (error) {
      console.warn('API获取统计数据失败:', error.message);
      // 返回默认数据，不让前端出错
      stats = {
        players: 0,
        skill_templates: 0,
        sword_heart_templates: 0,
        treasure_templates: 0,
        gacha_pools: 0,
        mails: 0
      };
    }

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('统计API错误:', error);
    res.status(500).json({
      success: false,
      error: '获取统计数据失败',
      code: 'STATS_ERROR'
    });
  }
});

// 详细数据统计面板API
app.get('/api/dashboard/statistics', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    console.log('🔄 开始获取详细统计数据...');
    
    const statistics = {
      overview: {},
      playerStats: {},
      levelDistribution: [],
      realmDistribution: [],
      resourceStats: {},
      activityStats: {},
      timestamp: new Date().toISOString()
    };

    // 获取基础统计数据
    try {
      // 总玩家数
      const totalPlayers = await dataService.count(tables.players, {});
      statistics.overview.totalPlayers = totalPlayers.count || 0;

      // 活跃玩家数（近7天有登录记录的）
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const activePlayers = await dataService.count(tables.players, {
        last_login: { $gte: sevenDaysAgo }
      });
      statistics.overview.activePlayers = activePlayers.count || 0;

      // 新注册玩家数（近7天）
      const newPlayers = await dataService.count(tables.players, {
        created_time: { $gte: sevenDaysAgo }
      });
      statistics.overview.newPlayers = newPlayers.count || 0;

    } catch (error) {
      console.warn('获取基础统计数据失败:', error.message);
      statistics.overview = {
        totalPlayers: 0,
        activePlayers: 0,
        newPlayers: 0
      };
    }

    // 获取等级分布数据
    try {
      const players = await dataService.getList(tables.players, {}, { limit: 1000 });
      const levelGroups = {};
      
      formatDbResult(players).forEach(player => {
        const level = player.level || 1;
        const group = Math.floor(level / 10) * 10; // 按10级分组
        const groupKey = `${group}-${group + 9}`;
        levelGroups[groupKey] = (levelGroups[groupKey] || 0) + 1;
      });

      statistics.levelDistribution = Object.entries(levelGroups).map(([range, count]) => ({
        range,
        count
      })).sort((a, b) => {
        const aStart = parseInt(a.range.split('-')[0]);
        const bStart = parseInt(b.range.split('-')[0]);
        return aStart - bStart;
      });

    } catch (error) {
      console.warn('获取等级分布数据失败:', error.message);
      statistics.levelDistribution = [];
    }

    // 获取境界分布数据
    try {
      const players = await dataService.getList(tables.players, {}, { limit: 1000 });
      const realmGroups = {};
      
      formatDbResult(players).forEach(player => {
        const realm = player.realm || '练气期';
        realmGroups[realm] = (realmGroups[realm] || 0) + 1;
      });

      statistics.realmDistribution = Object.entries(realmGroups).map(([realm, count]) => ({
        realm,
        count
      })).sort((a, b) => b.count - a.count);

    } catch (error) {
      console.warn('获取境界分布数据失败:', error.message);
      statistics.realmDistribution = [];
    }

    // 获取资源统计数据
    try {
      const resources = await dataService.getList(tables.playerResources, {}, { limit: 1000 });
      const resourceData = formatDbResult(resources);
      
      if (resourceData.length > 0) {
        const totalXianyu = resourceData.reduce((sum, r) => sum + (r.xianyu || 0), 0);
        const totalLingshi = resourceData.reduce((sum, r) => sum + (r.lingshi || 0), 0);
        const totalXiuwei = resourceData.reduce((sum, r) => sum + (r.xiuwei || 0), 0);
        
        statistics.resourceStats = {
          totalXianyu,
          totalLingshi,
          totalXiuwei,
          avgXianyu: Math.round(totalXianyu / resourceData.length),
          avgLingshi: Math.round(totalLingshi / resourceData.length),
          avgXiuwei: Math.round(totalXiuwei / resourceData.length)
        };
      } else {
        statistics.resourceStats = {
          totalXianyu: 0,
          totalLingshi: 0,
          totalXiuwei: 0,
          avgXianyu: 0,
          avgLingshi: 0,
          avgXiuwei: 0
        };
      }

    } catch (error) {
      console.warn('获取资源统计数据失败:', error.message);
      statistics.resourceStats = {
        totalXianyu: 0,
        totalLingshi: 0,
        totalXiuwei: 0,
        avgXianyu: 0,
        avgLingshi: 0,
        avgXiuwei: 0
      };
    }

    // 获取活动统计数据
    try {
      // 邮件统计
      const totalMails = await dataService.count(tables.mails, {});
      const unreadMails = await dataService.count(tables.playerMails, { is_read: false });
      
      statistics.activityStats = {
        totalMails: totalMails.count || 0,
        unreadMails: unreadMails.count || 0,
        totalTreasures: await dataService.count(tables.treasureTemplates, {}).then(r => r.count || 0),
        totalSkills: await dataService.count(tables.skills, {}).then(r => r.count || 0)
      };

    } catch (error) {
      console.warn('获取活动统计数据失败:', error.message);
      statistics.activityStats = {
        totalMails: 0,
        unreadMails: 0,
        totalTreasures: 0,
        totalSkills: 0
      };
    }

    console.log('✅ 详细统计数据获取完成');
    
    res.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('详细统计API错误:', error);
    res.status(500).json({
      success: false,
      error: '获取详细统计数据失败',
      code: 'DASHBOARD_STATS_ERROR',
      details: error.message
    });
  }
});

// ========== 功法模板管理API ==========

// 获取功法模板列表
app.get('/api/skill-templates', 
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'skill_template'),
  async (req, res) => {
    try {
      const { search, realm, rarity, page = 1, limit = 20 } = req.query;
      
      let query = { is_active: true };
      
      // 搜索条件
      if (search) {
        query.name = search;
      }
      
      if (realm) {
        query.realm = realm;
      }
      
      if (rarity) {
        query.rarity = parseInt(rarity);
      }
      
      const result = await dataService.getList(tables.skills, query, {
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
      res.json({
        success: true,
        data: formatDbResult(result),
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取功法模板列表失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 创建功法模板
app.post('/api/skill-templates', 
  authenticateToken,
  requirePermission('create'),
  auditLogger.createAuditMiddleware('CREATE', 'skill_template'),
  async (req, res) => {
    try {
      const templateData = {
        ...req.body,
        is_active: true,
        rarity: parseInt(req.body.rarity) || 1,
        max_level: parseInt(req.body.max_level) || 30
      };
      
      const result = await dataService.create(tables.skills, templateData);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('创建功法模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 更新功法模板
app.put('/api/skill-templates/:id', 
  authenticateToken,
  requirePermission('update'),
  auditLogger.createAuditMiddleware('UPDATE', 'skill_template'),
  async (req, res) => {
    try {
      const id = req.params.id;
      
      const updateData = {
        ...req.body,
        rarity: parseInt(req.body.rarity) || 1,
        max_level: parseInt(req.body.max_level) || 30
      };
      
      const result = await dataService.update(tables.skills, id, updateData);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('更新功法模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 删除功法模板（软删除）
app.delete('/api/skill-templates/:id', 
  authenticateToken,
  requirePermission('delete'),
  auditLogger.createAuditMiddleware('DELETE', 'skill_template'),
  async (req, res) => {
    try {
      const id = req.params.id;
      
      const result = await dataService.delete(tables.skills, id);
      
      res.json({
        success: true,
        message: '功法模板删除成功'
      });
    } catch (error) {
      console.error('删除功法模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// ========== 剑心模板管理API ==========

// 获取剑心模板列表
app.get('/api/sword-heart-templates',
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'sword_heart_template'),
  async (req, res) => {
    try {
      const { search, rarity, page = 1, limit = 20 } = req.query;
      
      let query = { is_active: true };
      
      // 搜索条件
      if (search) {
        query.name = search;
      }
      
      if (rarity) {
        query.rarity = parseInt(rarity);
      }
      
      const result = await dataService.getList(tables.swordHeartTemplates, query, {
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
      res.json({
        success: true,
        data: formatDbResult(result),
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取剑心模板列表失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 创建剑心模板
app.post('/api/sword-heart-templates',
  authenticateToken,
  auditLogger.createAuditMiddleware('CREATE', 'sword_heart_template'),
  async (req, res) => {
    try {
      const templateData = {
        ...req.body,
        is_active: true,
        rarity: parseInt(req.body.rarity) || 1,
        max_level: parseInt(req.body.max_level) || 50,
        max_advancement: parseInt(req.body.max_advancement) || 9
      };
      
      const result = await dataService.create(tables.swordHeartTemplates, templateData);
      
      await logAdminAction(
        'admin', 
        'CREATE', 
        'sword_heart_template', 
        result._id, 
        null, 
        templateData, 
        `创建剑心模板: ${templateData.name}`,
        req
      );
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('创建剑心模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 更新剑心模板
app.put('/api/sword-heart-templates/:id',
  authenticateToken,
  auditLogger.createAuditMiddleware('UPDATE', 'sword_heart_template'),
  async (req, res) => {
    try {
      const id = req.params.id;
      
      const updateData = {
        ...req.body,
        rarity: parseInt(req.body.rarity) || 1,
        max_level: parseInt(req.body.max_level) || 50,
        max_advancement: parseInt(req.body.max_advancement) || 9
      };
      
      const result = await dataService.update(tables.swordHeartTemplates, id, updateData);
      
      await logAdminAction(
        'admin', 
        'UPDATE', 
        'sword_heart_template', 
        id, 
        {}, 
        updateData, 
        `更新剑心模板: ${updateData.name}`,
        req
      );
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('更新剑心模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 删除剑心模板
app.delete('/api/sword-heart-templates/:id',
  authenticateToken,
  auditLogger.createAuditMiddleware('DELETE', 'sword_heart_template'),
  async (req, res) => {
    try {
      const id = req.params.id;
      
      const result = await dataService.delete(tables.swordHeartTemplates, id);
      
      await logAdminAction(
        'admin', 
        'DELETE', 
        'sword_heart_template', 
        id, 
        {}, 
        { is_active: false }, 
        `删除剑心模板`,
        req
      );
      
      res.json({
        success: true,
        message: '剑心模板删除成功'
      });
    } catch (error) {
      console.error('删除剑心模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// ========== 古宝模板管理API ==========

// 获取古宝模板列表
app.get('/api/treasure-templates',
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'treasure_template'),
  async (req, res) => {
    try {
      const { search, type, category, rarity, page = 1, limit = 20 } = req.query;
      
      let query = { is_active: true };
      
      // 搜索条件
      if (search) {
        query.name = search;
      }
      
      if (type) {
        query.type = type;
      }
      
      if (category) {
        query.category = category;
      }
      
      if (rarity) {
        query.rarity = parseInt(rarity);
      }
      
      const result = await dataService.getList(tables.treasureTemplates, query, {
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
      res.json({
        success: true,
        data: formatDbResult(result),
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取古宝模板列表失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 创建古宝模板
app.post('/api/treasure-templates',
  authenticateToken,
  auditLogger.createAuditMiddleware('CREATE', 'treasure_template'),
  async (req, res) => {
    try {
      const templateData = {
        ...req.body,
        is_active: true,
        rarity: parseInt(req.body.rarity) || 1,
        max_level: parseInt(req.body.max_level) || 100,
        max_star: parseInt(req.body.max_star) || 5
      };
      
      const result = await dataService.create(tables.treasureTemplates, templateData);
      
      await logAdminAction(
        'admin', 
        'CREATE', 
        'treasure_template', 
        result._id, 
        null, 
        templateData, 
        `创建古宝模板: ${templateData.name}`,
        req
      );
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('创建古宝模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 更新古宝模板
app.put('/api/treasure-templates/:id',
  authenticateToken,
  auditLogger.createAuditMiddleware('UPDATE', 'treasure_template'),
  async (req, res) => {
    try {
      const id = req.params.id;
      
      const updateData = {
        ...req.body,
        rarity: parseInt(req.body.rarity) || 1,
        max_level: parseInt(req.body.max_level) || 100,
        max_star: parseInt(req.body.max_star) || 5
      };
      
      const result = await dataService.update(tables.treasureTemplates, id, updateData);
      
      await logAdminAction(
        'admin', 
        'UPDATE', 
        'treasure_template', 
        id, 
        {}, 
        updateData, 
        `更新古宝模板: ${updateData.name}`,
        req
      );
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('更新古宝模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 删除古宝模板
app.delete('/api/treasure-templates/:id',
  authenticateToken,
  auditLogger.createAuditMiddleware('DELETE', 'treasure_template'),
  async (req, res) => {
    try {
      const id = req.params.id;
      
      const result = await dataService.delete(tables.treasureTemplates, id);
      
      await logAdminAction(
        'admin', 
        'DELETE', 
        'treasure_template', 
        id, 
        {}, 
        { is_active: false }, 
        `删除古宝模板`,
        req
      );
      
      res.json({
        success: true,
        message: '古宝模板删除成功'
      });
    } catch (error) {
      console.error('删除古宝模板失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// ========== 抽取池管理API ==========

// 获取抽取池列表
app.get('/api/gacha-pools', 
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'gacha_pool'),
  async (req, res) => {
    try {
      const { search, type, page = 1, limit = 20 } = req.query;
      
      let query = { is_active: true };
      
      if (search) {
        query.name = search;
      }
      
      if (type) {
        query.type = type;
      }
      
      const result = await dataService.getList(tables.gachaPools, query, {
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
      res.json({
        success: true,
        data: formatDbResult(result),
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取抽取池列表失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 创建抽取池
app.post('/api/gacha-pools', 
  authenticateToken,
  auditLogger.createAuditMiddleware('CREATE', 'gacha_pool'),
  async (req, res) => {
    try {
      const poolData = {
        ...req.body,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      };
      
      const result = await dataService.create(tables.gachaPools, poolData);
      
      await logAdminAction('admin', 'CREATE', 'gacha_pool', result._id, null, poolData, `创建抽取池: ${poolData.name}`, req);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('创建抽取池失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 更新抽取池
app.put('/api/gacha-pools/:id', 
  authenticateToken,
  auditLogger.createAuditMiddleware('UPDATE', 'gacha_pool'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = { ...req.body };
      delete updateData._id;
      delete updateData._openid;
      delete updateData.created_at;
      delete updateData.created_by;
      
      const result = await dataService.update(tables.gachaPools, id, updateData);
      
      await logAdminAction('admin', 'UPDATE', 'gacha_pool', id, {}, updateData, `更新抽取池: ${updateData.name}`, req);
      
      res.json({
        success: true,
        message: '抽取池更新成功'
      });
    } catch (error) {
      console.error('更新抽取池失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// 删除抽取池
app.delete('/api/gacha-pools/:id', 
  authenticateToken,
  auditLogger.createAuditMiddleware('DELETE', 'gacha_pool'),
  async (req, res) => {
    try {
      const { id } = req.params;
      
      const result = await dataService.delete(tables.gachaPools, id);
      
      await logAdminAction('admin', 'DELETE', 'gacha_pool', id, {}, { is_active: false }, `删除抽取池`, req);
      
      res.json({
        success: true,
        message: '抽取池删除成功'
      });
    } catch (error) {
      console.error('删除抽取池失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

// ========== 邮件模板管理API ==========

// 获取邮件模板列表
app.get('/api/mail-templates',
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'mail_template'),
  async (req, res) => {
    try {
      const { search, page = 1, limit = 10 } = req.query;
      const query = {};
      if (search) {
        query.title = { $regex: search, $options: 'i' };
      }

      const result = await dataService.getList(tables.mailTemplates, query, {
        page: parseInt(page),
        limit: parseInt(limit),
        orderBy: { created_at: 'desc' }
      });

      res.json({
        success: true,
        data: formatDbResult(result),
        total: result.total || 0,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取邮件模板列表失败:', error);
      res.status(500).json({ success: false, error: '获取邮件模板列表失败' });
    }
  }
);

// 创建邮件模板
app.post('/api/mail-templates',
  authenticateToken,
  auditLogger.createAuditMiddleware('CREATE', 'mail_template'),
  async (req, res) => {
    try {
      const { title, content, sender, attachments } = req.body;
      if (!title || !content) {
        return res.status(400).json({ success: false, error: '标题和内容不能为空' });
      }

      const templateData = {
        title,
        content,
        sender: sender || '系统管理员',
        attachments: attachments || [],
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await dataService.create(tables.mailTemplates, templateData);
      res.status(201).json({ success: true, data: result });
    } catch (error) {
      console.error('创建邮件模板失败:', error);
      res.status(500).json({ success: false, error: '创建邮件模板失败' });
    }
  }
);

// 更新邮件模板
app.put('/api/mail-templates/:id',
  authenticateToken,
  auditLogger.createAuditMiddleware('UPDATE', 'mail_template'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { title, content, sender, attachments } = req.body;
      if (!title || !content) {
        return res.status(400).json({ success: false, error: '标题和内容不能为空' });
      }
      
      const updateData = {
        title,
        content,
        sender,
        attachments,
        updated_at: new Date()
      };
      
      const result = await dataService.update(tables.mailTemplates, id, updateData);
      res.json({ success: true, data: result });
    } catch (error) {
      console.error('更新邮件模板失败:', error);
      res.status(500).json({ success: false, error: '更新邮件模板失败' });
    }
  }
);

// 删除邮件模板
app.delete('/api/mail-templates/:id',
  authenticateToken,
  auditLogger.createAuditMiddleware('DELETE', 'mail_template'),
  async (req, res) => {
    try {
      const { id } = req.params;
      await dataService.delete(tables.mailTemplates, id);
      res.json({ success: true, message: '邮件模板删除成功' });
    } catch (error) {
      console.error('删除邮件模板失败:', error);
      res.status(500).json({ success: false, error: '删除邮件模板失败' });
    }
  }
);

// ========== 玩家管理API ==========

// 获取玩家列表
app.get('/api/players', 
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'players'),
  async (req, res) => {
    try {
      const { search, page = 1, limit = 20, server_name, vip_level } = req.query;
      
      let query = {};
      
      // 搜索条件：支持按昵称或OpenID搜索
      if (search) {
        // 创建模糊匹配条件
        query.$or = [
          { nickname: { $regex: search, $options: 'i' } },
          { _openid: { $regex: search, $options: 'i' } }
        ];
      }
      
      // 服务器筛选
      if (server_name) {
        query.server_name = server_name;
      }
      
      // VIP等级筛选
      if (vip_level) {
        query.vip_level = parseInt(vip_level);
      }
      
      const result = await dataService.getList(tables.players, query, {
        page: parseInt(page),
        limit: parseInt(limit),
        orderBy: { createdAt: 'desc' } // 按创建时间倒序
      });
      
      res.json({
        success: true,
        data: formatDbResult(result),
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取玩家列表失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 获取单个玩家详情
app.get('/api/players/:id', 
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'player_detail'),
  async (req, res) => {
    try {
      const playerId = req.params.id;
      
      // 并行获取玩家基础信息和资源信息
      const [playerResult, resourceResult] = await Promise.all([
        cloudAdapter.get(tables.players, { _id: playerId }),
        cloudAdapter.get(tables.playerRes, { player_id: playerId })
      ]);
      
      if (!playerResult.success || !playerResult.data) {
        return res.status(404).json({
          success: false,
          error: '玩家不存在'
        });
      }
      
      // 合并玩家数据和资源数据
      const playerData = {
        ...playerResult.data,
        resources: resourceResult.success ? resourceResult.data : null
      };
      
      res.json({
        success: true,
        data: playerData
      });
    } catch (error) {
      console.error('获取玩家详情失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 更新玩家基础信息
app.put('/api/players/:id', 
  authenticateToken,
  requirePermission('update'),
  auditLogger.createAuditMiddleware('UPDATE', 'player_info'),
  async (req, res) => {
    try {
      const playerId = req.params.id;
      const updateData = req.body;
      
      // 获取更新前的数据用于审计
      const beforeResult = await cloudAdapter.get(tables.players, { _id: playerId });
      const beforeData = beforeResult.data || {};
      
      // 只允许更新特定字段，防止恶意修改
      const allowedFields = [
        'nickname', 'level', 'cultivation_realm', 'avatar_url', 
        'server_name', 'vip_level', 'is_banned'
      ];
      
      const filteredData = {};
      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          filteredData[field] = updateData[field];
        }
      });
      
      // 数据验证
      if (filteredData.level && (filteredData.level < 1 || filteredData.level > 1000)) {
        return res.status(400).json({
          success: false,
          error: '等级必须在1-1000之间'
        });
      }
      
      if (filteredData.vip_level && (filteredData.vip_level < 0 || filteredData.vip_level > 20)) {
        return res.status(400).json({
          success: false,
          error: 'VIP等级必须在0-20之间'
        });
      }
      
      const result = await dataService.update(tables.players, playerId, filteredData);
      
      res.json({
        success: true,
        data: result,
        message: '玩家信息更新成功'
      });
    } catch (error) {
      console.error('更新玩家信息失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 更新玩家资源
app.put('/api/players/:id/resources', 
  authenticateToken,
  requirePermission('update'),
  auditLogger.createAuditMiddleware('UPDATE', 'player_resources'),
  async (req, res) => {
    try {
      const playerId = req.params.id;
      const updateData = req.body;
      
      // 获取更新前的数据用于审计
      const beforeResult = await cloudAdapter.get(tables.playerRes, { player_id: playerId });
      const beforeData = beforeResult.data || {};
      
      // 只允许更新特定资源字段
      const allowedFields = [
        'xianyu', 'lingshi', 'lianlidian', 'sword_intent', 
        'arena_score', 'battle_power', 'cultivation_progress'
      ];
      
      const filteredData = {};
      allowedFields.forEach(field => {
        if (updateData.hasOwnProperty(field)) {
          const value = parseInt(updateData[field]);
          if (!isNaN(value) && value >= 0) {
            filteredData[field] = value;
          }
        }
      });
      
      const result = await dataService.update(tables.playerRes, beforeData._id, filteredData);
      
      res.json({
        success: true,
        data: result,
        message: '玩家资源更新成功'
      });
    } catch (error) {
      console.error('更新玩家资源失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 发送邮件(升级版)
app.post('/api/send-mail',
  authenticateToken,
  auditLogger.createAuditMiddleware('CREATE', 'batch_mail'),
  async (req, res) => {
    try {
      const { recipientsType, recipientIds, title, content, attachments } = req.body;

      if (!title || !content) {
        return res.status(400).json({ success: false, error: '标题和内容不能为空' });
      }

      let targetPlayers = [];
      if (recipientsType === 'all') {
        const result = await dataService.getList(tables.players, {}, { limit: 10000, fields: { _openid: 1 } });
        targetPlayers = result.data.map(p => p._openid);
      } else if (recipientsType === 'selected' && recipientIds && recipientIds.length > 0) {
        targetPlayers = recipientIds;
      } else {
        return res.status(400).json({ success: false, error: '无效的收件人' });
      }

      if (targetPlayers.length === 0) {
        return res.status(400).json({ success: false, error: '没有找到任何收件人' });
      }

      // 首先在mails表中创建邮件记录
      const mailRecord = {
        title,
        content,
        sender: req.session.user.username || '系统管理员',
        rewards: attachments || [],
        has_rewards: attachments && attachments.length > 0,
        created_at: new Date(),
        updated_at: new Date()
      };

      const mailResult = await dataService.create(tables.mails, mailRecord);
      const mailId = mailResult.data._id;

      // 然后为每个玩家创建邮件接收记录
      const mailData = {
        mail_id: mailId,
        title,
        content,
        sender: req.session.user.username || '系统管理员',
        rewards: attachments || [],
        has_rewards: attachments && attachments.length > 0,
        is_read: false,
        is_claimed: false,
        deleted: false,
        created_at: new Date(),
        updated_at: new Date()
      };

      const mailPromises = targetPlayers.map(openid => {
        return dataService.create(tables.playerMails, {
          ...mailData,
          _openid: openid,
        });
      });

      const results = await Promise.allSettled(mailPromises);
      const successfulSends = results.filter(r => r.status === 'fulfilled').length;
      
      await logAdminAction(
        req.session.user.username, 
        'CREATE', 
        'batch_mail', 
        null, 
        null, 
        { ...mailData, recipientsCount: targetPlayers.length },
        `批量发送邮件: "${title}" 给 ${targetPlayers.length} 名玩家`, 
        req
      );

      res.json({
        success: true,
        message: `成功向 ${successfulSends}/${targetPlayers.length} 名玩家发送邮件。`
      });

    } catch (error) {
      console.error('发送邮件失败:', error);
      res.status(500).json({ success: false, error: '服务器内部错误' });
    }
  }
);

// ========== 邮件状态跟踪API ==========

// 获取邮件发送历史
app.get('/api/mails/history',
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'mail_history'),
  async (req, res) => {
    try {
      const { page = 1, limit = 20, search } = req.query;
      let query = {};
      
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { sender: { $regex: search, $options: 'i' } }
        ];
      }

      const result = await dataService.getList(tables.mails, query, {
        page: parseInt(page),
        limit: parseInt(limit),
        orderBy: { created_at: 'desc' }
      });

      // 为每封邮件获取发送统计信息
      const mailsWithStats = await Promise.all(
        result.data.map(async (mail) => {
          try {
            const totalResult = await dataService.count(tables.playerMails, { mail_id: mail._id });
            const readResult = await dataService.count(tables.playerMails, { mail_id: mail._id, is_read: true });
            const claimedResult = await dataService.count(tables.playerMails, { mail_id: mail._id, is_claimed: true });

            return {
              ...mail,
              stats: {
                total_sent: totalResult.count || 0,
                total_read: readResult.count || 0,
                total_claimed: claimedResult.count || 0,
                read_rate: totalResult.count > 0 ? ((readResult.count || 0) / totalResult.count * 100).toFixed(1) : '0',
                claim_rate: totalResult.count > 0 ? ((claimedResult.count || 0) / totalResult.count * 100).toFixed(1) : '0'
              }
            };
          } catch (error) {
            console.warn(`获取邮件 ${mail._id} 统计信息失败:`, error.message);
            return {
              ...mail,
              stats: {
                total_sent: 0,
                total_read: 0,
                total_claimed: 0,
                read_rate: '0',
                claim_rate: '0'
              }
            };
          }
        })
      );

      res.json({
        success: true,
        data: mailsWithStats,
        total: result.total || 0,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取邮件历史失败:', error);
      res.status(500).json({ success: false, error: '获取邮件历史失败' });
    }
  }
);

// 获取单个邮件详情和接收状态
app.get('/api/mails/:mailId/recipients',
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'mail_recipients'),
  async (req, res) => {
    try {
      const { mailId } = req.params;
      const { page = 1, limit = 50, status } = req.query;

      let query = { mail_id: mailId };
      if (status) {
        switch (status) {
          case 'unread':
            query.is_read = false;
            break;
          case 'read':
            query.is_read = true;
            break;
          case 'claimed':
            query.is_claimed = true;
            break;
          case 'unclaimed':
            query.is_claimed = false;
            query.has_rewards = true;
            break;
        }
      }

      const result = await dataService.getList(tables.playerMails, query, {
        page: parseInt(page),
        limit: parseInt(limit),
        orderBy: { created_at: 'desc' }
      });

      // 获取玩家昵称信息
      const recipientsWithNames = await Promise.all(
        result.data.map(async (recipient) => {
          try {
            const playerResult = await dataService.get(tables.players, { _openid: recipient._openid });
            return {
              ...recipient,
              player_nickname: playerResult.success ? playerResult.data.nickname : '未知玩家'
            };
          } catch (error) {
            return {
              ...recipient,
              player_nickname: '未知玩家'
            };
          }
        })
      );

      res.json({
        success: true,
        data: recipientsWithNames,
        total: result.total || 0,
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取邮件接收者列表失败:', error);
      res.status(500).json({ success: false, error: '获取邮件接收者列表失败' });
    }
  }
);

// 删除邮件（软删除）
app.delete('/api/mails/:mailId',
  authenticateToken,
  auditLogger.createAuditMiddleware('DELETE', 'mail'),
  async (req, res) => {
    try {
      const { mailId } = req.params;

      // 软删除邮件模板
      await dataService.update(tables.mails, mailId, {
        deleted: true,
        deleted_at: new Date(),
        updated_at: new Date()
      });

      // 软删除所有相关的玩家邮件记录
      const recipientsResult = await dataService.getList(tables.playerMails, { mail_id: mailId });
      if (recipientsResult.success && recipientsResult.data.length > 0) {
        const updatePromises = recipientsResult.data.map(recipient => 
          dataService.update(tables.playerMails, recipient._id, {
            deleted: true,
            deleted_at: new Date(),
            updated_at: new Date()
          })
        );
        await Promise.allSettled(updatePromises);
      }

      await logAdminAction(
        req.session.user.username,
        'DELETE',
        'mail',
        mailId,
        null,
        { deleted: true },
        `删除邮件: ID ${mailId}`,
        req
      );

      res.json({ success: true, message: '邮件删除成功' });
    } catch (error) {
      console.error('删除邮件失败:', error);
      res.status(500).json({ success: false, error: '删除邮件失败' });
    }
  }
);

// 批量重发邮件给未读玩家
app.post('/api/mails/:mailId/resend',
  authenticateToken,
  auditLogger.createAuditMiddleware('CREATE', 'mail_resend'),
  async (req, res) => {
    try {
      const { mailId } = req.params;

      // 获取原邮件信息
      const mailResult = await dataService.get(tables.mails, { _id: mailId });
      if (!mailResult.success) {
        return res.status(404).json({ success: false, error: '邮件不存在' });
      }

      const originalMail = mailResult.data;

      // 获取未读该邮件的玩家
      const unreadRecipientsResult = await dataService.getList(tables.playerMails, {
        mail_id: mailId,
        is_read: false,
        deleted: false
      });

      if (!unreadRecipientsResult.success || unreadRecipientsResult.data.length === 0) {
        return res.json({ success: true, message: '没有需要重发的未读玩家' });
      }

      // 创建新的邮件记录
      const newMailRecord = {
        title: `[重发] ${originalMail.title}`,
        content: originalMail.content,
        sender: req.session.user.username || '系统管理员',
        rewards: originalMail.rewards || [],
        has_rewards: originalMail.has_rewards || false,
        original_mail_id: mailId,
        created_at: new Date(),
        updated_at: new Date()
      };

      const newMailResult = await dataService.create(tables.mails, newMailRecord);
      const newMailId = newMailResult.data._id;

      // 为未读玩家创建新的邮件接收记录
      const resendPromises = unreadRecipientsResult.data.map(recipient => {
        return dataService.create(tables.playerMails, {
          ...newMailRecord,
          mail_id: newMailId,
          _openid: recipient._openid,
          is_read: false,
          is_claimed: false,
          deleted: false
        });
      });

      const results = await Promise.allSettled(resendPromises);
      const successfulResends = results.filter(r => r.status === 'fulfilled').length;

      await logAdminAction(
        req.session.user.username,
        'CREATE',
        'mail_resend',
        newMailId,
        { original_mail_id: mailId },
        { recipients_count: successfulResends },
        `重发邮件: "${originalMail.title}" 给 ${successfulResends} 名未读玩家`,
        req
      );

      res.json({
        success: true,
        message: `成功重发邮件给 ${successfulResends}/${unreadRecipientsResult.data.length} 名未读玩家`,
        new_mail_id: newMailId
      });

    } catch (error) {
      console.error('重发邮件失败:', error);
      res.status(500).json({ success: false, error: '重发邮件失败' });
    }
  }
);

// 获取玩家统计信息
app.get('/api/players/stats/overview', 
  authenticateToken,
  auditLogger.createAuditMiddleware('READ', 'player_stats'),
  async (req, res) => {
    try {
      // 获取基础统计
      const totalPlayersResult = await cloudAdapter.count(tables.players, {});
      const activePlayersResult = await cloudAdapter.count(tables.players, { 
        last_login_time: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } 
      });
      const vipPlayersResult = await cloudAdapter.count(tables.players, { 
        vip_level: { $gt: 0 } 
      });
      
      // 等级分布统计（简化版）
      const levelDistribution = {
        'level_1_10': 0,
        'level_11_30': 0,
        'level_31_60': 0,
        'level_61_100': 0,
        'level_100_plus': 0
      };
      
      res.json({
        success: true,
        data: {
          totalPlayers: totalPlayersResult.count || 0,
          activePlayers: activePlayersResult.count || 0,
          vipPlayers: vipPlayersResult.count || 0,
          levelDistribution
        }
      });
    } catch (error) {
      console.error('获取玩家统计失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// 获取玩家资源列表
app.get('/api/players/resources', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { page = 1, limit = 20, search = '' } = req.query;
    const skip = (page - 1) * limit;

    // 获取玩家列表和资源信息
    let playersQuery = {};
    if (search) {
      playersQuery = {
        $or: [
          { nickname: { $regex: search, $options: 'i' } },
          { _openid: { $regex: search, $options: 'i' } }
        ]
      };
    }

    // 获取玩家基础信息
    const playersResult = await dataService.getList(config.tables.players, playersQuery, {
      skip: parseInt(skip),
      limit: parseInt(limit)
    });

    if (!playersResult.success) {
      throw new Error('获取玩家列表失败');
    }

    // 为每个玩家获取资源信息
    const playersWithResources = await Promise.all(
      playersResult.data.map(async (player) => {
        try {
          const resourceResult = await dataService.get(config.tables.playerRes, { player_id: player._id });
          return {
            ...player,
            resources: resourceResult.success ? resourceResult.data : {
              xianyu: 0,
              lingshi: 0,
              lianlidian: 0,
              sword_intent: 0,
              arena_score: 0,
              battle_power: 0,
              cultivation_progress: 0
            }
          };
        } catch (error) {
          console.warn(`获取玩家 ${player.nickname} 资源失败:`, error.message);
          return {
            ...player,
            resources: {
              xianyu: 0,
              lingshi: 0,
              lianlidian: 0,
              sword_intent: 0,
              arena_score: 0,
              battle_power: 0,
              cultivation_progress: 0
            }
          };
        }
      })
    );

    // 获取总数
    const totalResult = await dataService.count(config.tables.players, playersQuery);
    const total = totalResult.success ? totalResult.count : 0;

    res.json({
      success: true,
      data: {
        players: playersWithResources,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取玩家资源列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取玩家资源列表失败',
      code: 'RESOURCES_LIST_ERROR'
    });
  }
});

// 批量更新玩家资源
app.post('/api/players/resources/batch', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { playerIds, updates, operation = 'set' } = req.body;

    if (!playerIds || !Array.isArray(playerIds) || playerIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: '玩家ID列表不能为空'
      });
    }

    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        success: false,
        error: '更新数据不能为空'
      });
    }

    // 验证资源字段
    const allowedFields = [
      'xianyu', 'lingshi', 'lianlidian', 'sword_intent', 
      'arena_score', 'battle_power', 'cultivation_progress'
    ];

    const filteredUpdates = {};
    for (const field of allowedFields) {
      if (updates.hasOwnProperty(field)) {
        const value = parseInt(updates[field]);
        if (!isNaN(value) && value >= 0) {
          filteredUpdates[field] = value;
        }
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有有效的更新字段'
      });
    }

    // 批量更新资源
    const updatePromises = playerIds.map(async (playerId) => {
      try {
        // 获取当前资源数据
        const currentResult = await dataService.get(config.tables.playerRes, { player_id: playerId });
        
        if (!currentResult.success) {
          throw new Error(`玩家 ${playerId} 资源数据不存在`);
        }

        const currentData = currentResult.data;
        let newData = {};

        if (operation === 'add') {
          // 增加操作
          for (const [field, value] of Object.entries(filteredUpdates)) {
            newData[field] = (currentData[field] || 0) + value;
          }
        } else if (operation === 'subtract') {
          // 减少操作
          for (const [field, value] of Object.entries(filteredUpdates)) {
            newData[field] = Math.max(0, (currentData[field] || 0) - value);
          }
        } else {
          // 设置操作（默认）
          newData = filteredUpdates;
        }

        const updateResult = await dataService.update(config.tables.playerRes, currentData._id, newData);
        return {
          playerId,
          success: updateResult.success,
          error: updateResult.success ? null : updateResult.error
        };
      } catch (error) {
        return {
          playerId,
          success: false,
          error: error.message
        };
      }
    });

    const results = await Promise.allSettled(updatePromises);
    
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.filter(r => r.status === 'rejected' || !r.value.success).length;

    // 记录操作日志（异步）
    setImmediate(async () => {
      try {
        const logData = {
          admin_id: req.session.user.username,
          admin_username: req.session.user.username,
          action: 'BATCH_UPDATE_RESOURCES',
          target_type: 'player_resources',
          target_id: playerIds.join(','),
          status: 'success',
          description: `批量${operation === 'add' ? '增加' : operation === 'subtract' ? '减少' : '设置'}玩家资源，成功${successful}个，失败${failed}个`,
          ip_address: req.ip || 'unknown',
          user_agent: req.get('User-Agent') || '',
          timestamp: new Date()
        };
        
        // 这里可以调用审计日志记录，暂时使用console记录
        console.log('批量资源操作记录:', logData);
      } catch (error) {
        console.warn('批量资源操作日志记录失败:', error.message);
      }
    });

    res.json({
      success: true,
      message: `批量资源更新完成：成功 ${successful} 个，失败 ${failed} 个`,
      details: {
        total: playerIds.length,
        successful,
        failed,
        operation,
        updates: filteredUpdates
      }
    });

  } catch (error) {
    console.error('批量更新玩家资源失败:', error);
    res.status(500).json({
      success: false,
      error: '批量更新玩家资源失败',
      code: 'BATCH_UPDATE_ERROR'
    });
  }
});

// ========== 玩家装备和技能管理API ==========

// 获取玩家装备和技能列表
app.get('/api/players/equipment', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { page = 1, limit = 20, search = '', type = 'all' } = req.query;
    const skip = (page - 1) * limit;

    // 获取玩家列表
    let playersQuery = {};
    if (search) {
      playersQuery = {
        $or: [
          { nickname: { $regex: search, $options: 'i' } },
          { _openid: { $regex: search, $options: 'i' } }
        ]
      };
    }

    // 获取玩家基础信息
    const playersResult = await dataService.getList(config.tables.players, playersQuery, {
      skip: parseInt(skip),
      limit: parseInt(limit)
    });

    if (!playersResult.success) {
      throw new Error('获取玩家列表失败');
    }

    // 为每个玩家获取装备和技能信息
    const playersWithEquipment = await Promise.all(
      playersResult.data.map(async (player) => {
        try {
          const [treasuresResult, skillsResult] = await Promise.all([
            dataService.getList(config.tables.playerTreasures, { player_id: player._id }),
            dataService.getList(config.tables.playerSkill, { player_id: player._id })
          ]);

          return {
            ...player,
            treasures: treasuresResult.success ? treasuresResult.data : [],
            skills: skillsResult.success ? skillsResult.data : [],
            equipmentCount: (treasuresResult.success ? treasuresResult.data.length : 0) + 
                           (skillsResult.success ? skillsResult.data.length : 0)
          };
        } catch (error) {
          console.warn(`获取玩家 ${player.nickname} 装备技能失败:`, error.message);
          return {
            ...player,
            treasures: [],
            skills: [],
            equipmentCount: 0
          };
        }
      })
    );

    // 根据类型筛选
    let filteredPlayers = playersWithEquipment;
    if (type === 'treasures') {
      filteredPlayers = playersWithEquipment.filter(p => p.treasures.length > 0);
    } else if (type === 'skills') {
      filteredPlayers = playersWithEquipment.filter(p => p.skills.length > 0);
    }

    // 获取总数
    const totalResult = await dataService.count(config.tables.players, playersQuery);
    const total = totalResult.success ? totalResult.count : 0;

    res.json({
      success: true,
      data: {
        players: filteredPlayers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取玩家装备技能列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取玩家装备技能列表失败',
      code: 'EQUIPMENT_LIST_ERROR'
    });
  }
});

// 获取单个玩家的装备详情
app.get('/api/players/:playerId/treasures', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { playerId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    // 获取玩家装备列表
    const treasuresResult = await dataService.getList(config.tables.playerTreasures, 
      { player_id: playerId },
      {
        skip: (page - 1) * limit,
        limit: parseInt(limit)
      }
    );

    if (!treasuresResult.success) {
      throw new Error('获取玩家装备失败');
    }

    // 获取总数
    const totalResult = await dataService.count(config.tables.playerTreasures, { player_id: playerId });

    res.json({
      success: true,
      data: {
        treasures: treasuresResult.data || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult.success ? totalResult.count : 0,
          pages: Math.ceil((totalResult.success ? totalResult.count : 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取玩家装备详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取玩家装备详情失败',
      code: 'TREASURES_DETAIL_ERROR'
    });
  }
});

// 获取单个玩家的技能详情
app.get('/api/players/:playerId/skills', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { playerId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    // 获取玩家技能列表
    const skillsResult = await dataService.getList(config.tables.playerSkill, 
      { player_id: playerId },
      {
        skip: (page - 1) * limit,
        limit: parseInt(limit)
      }
    );

    if (!skillsResult.success) {
      throw new Error('获取玩家技能失败');
    }

    // 获取总数
    const totalResult = await dataService.count(config.tables.playerSkill, { player_id: playerId });

    res.json({
      success: true,
      data: {
        skills: skillsResult.data || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult.success ? totalResult.count : 0,
          pages: Math.ceil((totalResult.success ? totalResult.count : 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取玩家技能详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取玩家技能详情失败',
      code: 'SKILLS_DETAIL_ERROR'
    });
  }
});

// 更新玩家装备
app.put('/api/players/:playerId/treasures/:treasureId', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { playerId, treasureId } = req.params;
    const updateData = req.body;

    // 验证更新字段
    const allowedFields = [
      'level', 'star', 'experience', 'equipped', 'enhance_level'
    ];

    const filteredUpdates = {};
    for (const field of allowedFields) {
      if (updateData.hasOwnProperty(field)) {
        if (field === 'equipped') {
          filteredUpdates[field] = Boolean(updateData[field]);
        } else {
          const value = parseInt(updateData[field]);
          if (!isNaN(value) && value >= 0) {
            filteredUpdates[field] = value;
          }
        }
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有有效的更新字段'
      });
    }

    // 执行更新
    const updateResult = await dataService.update(config.tables.playerTreasures, treasureId, filteredUpdates);

    if (!updateResult.success) {
      throw new Error(updateResult.error || '更新装备失败');
    }

    // 记录操作日志
    setImmediate(async () => {
      try {
        console.log('装备更新操作记录:', {
          admin: req.session.user.username,
          playerId,
          treasureId,
          updates: filteredUpdates
        });
      } catch (error) {
        console.warn('装备更新日志记录失败:', error.message);
      }
    });

    res.json({
      success: true,
      message: '装备更新成功',
      data: filteredUpdates
    });

  } catch (error) {
    console.error('更新玩家装备失败:', error);
    res.status(500).json({
      success: false,
      error: '更新玩家装备失败',
      code: 'UPDATE_TREASURE_ERROR'
    });
  }
});

// 更新玩家技能
app.put('/api/players/:playerId/skills/:skillId', async (req, res) => {
  try {
    // 简单的session检查
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { playerId, skillId } = req.params;
    const updateData = req.body;

    // 验证更新字段
    const allowedFields = [
      'level', 'experience', 'equipped', 'skill_points'
    ];

    const filteredUpdates = {};
    for (const field of allowedFields) {
      if (updateData.hasOwnProperty(field)) {
        if (field === 'equipped') {
          filteredUpdates[field] = Boolean(updateData[field]);
        } else {
          const value = parseInt(updateData[field]);
          if (!isNaN(value) && value >= 0) {
            filteredUpdates[field] = value;
          }
        }
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有有效的更新字段'
      });
    }

    // 执行更新
    const updateResult = await dataService.update(config.tables.playerSkill, skillId, filteredUpdates);

    if (!updateResult.success) {
      throw new Error(updateResult.error || '更新技能失败');
    }

    // 记录操作日志
    setImmediate(async () => {
      try {
        console.log('技能更新操作记录:', {
          admin: req.session.user.username,
          playerId,
          skillId,
          updates: filteredUpdates
        });
      } catch (error) {
        console.warn('技能更新日志记录失败:', error.message);
      }
    });

    res.json({
      success: true,
      message: '技能更新成功',
      data: filteredUpdates
    });

  } catch (error) {
    console.error('更新玩家技能失败:', error);
    res.status(500).json({
      success: false,
      error: '更新玩家技能失败',
      code: 'UPDATE_SKILL_ERROR'
    });
  }
});

// ========== 系统监控和日志管理API ==========

// 系统状态监控API
app.get('/api/system/status', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    // 计算系统运行时间
    const uptime = process.uptime();
    
    // 模拟平均响应时间
    const avgResponseTime = Math.floor(Math.random() * 100) + 50;
    
    // 统计24小时内的错误数量（模拟数据）
    const errorCount = Math.floor(Math.random() * 10);
    
    // 获取当前活跃管理员数量（简化为1）
    const activeAdmins = 1;
    
    // 生成24小时日志趋势数据
    const logTrend = [];
    for (let i = 23; i >= 0; i--) {
      const hour = new Date();
      hour.setHours(hour.getHours() - i);
      logTrend.push({
        hour: hour.getHours(),
        count: Math.floor(Math.random() * 50) + 10
      });
    }
    
    // 日志级别统计
    const logLevelStats = {
      info: Math.floor(Math.random() * 100) + 50,
      warn: Math.floor(Math.random() * 30) + 10,
      error: Math.floor(Math.random() * 10) + 1,
      debug: Math.floor(Math.random() * 20) + 5
    };

    res.json({
      success: true,
      data: {
        status: '正常',
        uptime: uptime,
        avgResponseTime: avgResponseTime,
        errorCount: errorCount,
        activeAdmins: activeAdmins,
        logTrend: logTrend,
        logLevelStats: logLevelStats
      }
    });

  } catch (error) {
    console.error('获取系统状态失败:', error);
    res.status(500).json({
      success: false,
      error: '获取系统状态失败',
      code: 'SYSTEM_STATUS_ERROR'
    });
  }
});

// 获取日志列表API
app.get('/api/logs', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const {
      page = 1,
      pageSize = 20,
      level = '',
      module = '',
      startDate = '',
      endDate = '',
      keyword = '',
      admin = ''
    } = req.query;

    // 构建查询条件
    const conditions = {};
    
    if (level) conditions.level = level;
    if (module) conditions.module = module;
    if (admin) conditions.admin_user = { $regex: admin, $options: 'i' };
    if (keyword) conditions.message = { $regex: keyword, $options: 'i' };
    
    if (startDate || endDate) {
      conditions.timestamp = {};
      if (startDate) conditions.timestamp.$gte = new Date(startDate);
      if (endDate) conditions.timestamp.$lte = new Date(endDate);
    }

    try {
      // 尝试从admin_logs表获取日志
      const logs = await dataService.getList(tables.adminLogs || 'admin_logs', conditions, {
        limit: parseInt(pageSize),
        offset: (parseInt(page) - 1) * parseInt(pageSize),
        sort: { timestamp: -1 }
      });
      
      const totalCount = await dataService.count(tables.adminLogs || 'admin_logs', conditions);
      
      res.json({
        success: true,
        data: {
          logs: formatDbResult(logs),
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: totalCount.count || 0,
            totalPages: Math.ceil((totalCount.count || 0) / parseInt(pageSize))
          }
        }
      });

    } catch (error) {
      // 如果日志表不存在或查询失败，返回模拟数据
      console.warn('获取日志数据失败，返回模拟数据:', error.message);
      
      const mockLogs = [];
      const totalLogs = 100; // 模拟总日志数
      const startIndex = (parseInt(page) - 1) * parseInt(pageSize);
      
      for (let i = 0; i < parseInt(pageSize) && startIndex + i < totalLogs; i++) {
        const logTime = new Date();
        logTime.setMinutes(logTime.getMinutes() - (startIndex + i) * 5);
        
        const levels = ['info', 'warn', 'error', 'debug'];
        const modules = ['auth', 'player', 'mail', 'template', 'system'];
        const admins = ['admin', 'manager', 'operator'];
        
        mockLogs.push({
          _id: `log_${startIndex + i + 1}`,
          timestamp: logTime.toISOString(),
          level: levels[Math.floor(Math.random() * levels.length)],
          module: modules[Math.floor(Math.random() * modules.length)],
          admin_user: admins[Math.floor(Math.random() * admins.length)],
          message: `系统操作记录 ${startIndex + i + 1}: 执行了某项操作`,
          ip_address: `192.168.1.${Math.floor(Math.random() * 255)}`,
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        });
      }
      
      res.json({
        success: true,
        data: {
          logs: mockLogs,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: totalLogs,
            totalPages: Math.ceil(totalLogs / parseInt(pageSize))
          }
        }
      });
    }

  } catch (error) {
    console.error('获取日志列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取日志列表失败',
      code: 'LOGS_LIST_ERROR'
    });
  }
});

// 获取单个日志详情API
app.get('/api/logs/:logId', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { logId } = req.params;

    try {
      const log = await dataService.get(tables.adminLogs || 'admin_logs', { _id: logId });
      
      if (!log.success || !log.data) {
        return res.status(404).json({
          success: false,
          error: '日志记录不存在',
          code: 'LOG_NOT_FOUND'
        });
      }

      res.json({
        success: true,
        data: log.data
      });

    } catch (error) {
      // 返回模拟的日志详情
      const mockLog = {
        _id: logId,
        timestamp: new Date().toISOString(),
        level: 'info',
        module: 'system',
        admin_user: 'admin',
        message: '这是一条模拟的日志详情记录',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        data: {
          action: 'view_log_detail',
          target: 'log_system',
          additional_info: '模拟数据'
        }
      };

      res.json({
        success: true,
        data: mockLog
      });
    }

  } catch (error) {
    console.error('获取日志详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取日志详情失败',
      code: 'LOG_DETAIL_ERROR'
    });
  }
});

// 导出日志API
app.get('/api/logs/export', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const {
      level = '',
      module = '',
      startDate = '',
      endDate = '',
      keyword = '',
      admin = ''
    } = req.query;

    // 构建查询条件
    const conditions = {};
    if (level) conditions.level = level;
    if (module) conditions.module = module;
    if (admin) conditions.admin_user = { $regex: admin, $options: 'i' };
    if (keyword) conditions.message = { $regex: keyword, $options: 'i' };
    
    if (startDate || endDate) {
      conditions.timestamp = {};
      if (startDate) conditions.timestamp.$gte = new Date(startDate);
      if (endDate) conditions.timestamp.$lte = new Date(endDate);
    }

    // 生成CSV内容
    let csvContent = '时间,级别,模块,管理员,操作内容,IP地址\n';
    
    try {
      const logs = await dataService.getList(tables.adminLogs || 'admin_logs', conditions, {
        limit: 10000, // 限制导出数量
        sort: { timestamp: -1 }
      });
      
      formatDbResult(logs).forEach(log => {
        const row = [
          new Date(log.timestamp).toLocaleString('zh-CN'),
          log.level || '',
          log.module || '',
          log.admin_user || '',
          (log.message || '').replace(/"/g, '""'), // 转义双引号
          log.ip_address || ''
        ];
        csvContent += `"${row.join('","')}"\n`;
      });

    } catch (error) {
      // 生成模拟数据
      for (let i = 0; i < 100; i++) {
        const logTime = new Date();
        logTime.setMinutes(logTime.getMinutes() - i * 5);
        
        const levels = ['info', 'warn', 'error', 'debug'];
        const modules = ['auth', 'player', 'mail', 'template', 'system'];
        const admins = ['admin', 'manager', 'operator'];
        
        const row = [
          logTime.toLocaleString('zh-CN'),
          levels[Math.floor(Math.random() * levels.length)],
          modules[Math.floor(Math.random() * modules.length)],
          admins[Math.floor(Math.random() * admins.length)],
          `系统操作记录 ${i + 1}: 执行了某项操作`,
          `192.168.1.${Math.floor(Math.random() * 255)}`
        ];
        csvContent += `"${row.join('","')}"\n`;
      }
    }

    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=logs_${new Date().toISOString().slice(0, 10)}.csv`);
    res.send('\ufeff' + csvContent); // 添加BOM以支持中文

  } catch (error) {
    console.error('导出日志失败:', error);
    res.status(500).json({
      success: false,
      error: '导出日志失败',
      code: 'LOGS_EXPORT_ERROR'
    });
  }
});

// 清理旧日志API
app.post('/api/logs/cleanup', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { days = 30 } = req.body;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

    try {
      // 尝试删除旧日志
      const deleteResult = await dataService.delete(tables.adminLogs || 'admin_logs', {
        timestamp: { $lt: cutoffDate }
      });

      res.json({
        success: true,
        message: `成功清理了 ${deleteResult.deletedCount || 0} 条旧日志`,
        data: {
          deletedCount: deleteResult.deletedCount || 0,
          cutoffDate: cutoffDate.toISOString()
        }
      });

    } catch (error) {
      // 返回模拟结果
      const mockDeletedCount = Math.floor(Math.random() * 100) + 50;
      
      res.json({
        success: true,
        message: `模拟清理了 ${mockDeletedCount} 条旧日志`,
        data: {
          deletedCount: mockDeletedCount,
          cutoffDate: cutoffDate.toISOString()
        }
      });
    }

  } catch (error) {
    console.error('清理旧日志失败:', error);
    res.status(500).json({
      success: false,
      error: '清理旧日志失败',
      code: 'LOGS_CLEANUP_ERROR'
    });
  }
});

// ========== 批量操作和数据管理API ==========

// 数据导入API
app.post('/api/batch/import', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    // 模拟文件处理过程
    const { type, skipDuplicates, validateData } = req.body;
    
    // 模拟导入结果
    const mockResults = {
      imported: Math.floor(Math.random() * 100) + 50,
      skipped: skipDuplicates ? Math.floor(Math.random() * 10) : 0,
      errors: validateData ? Math.floor(Math.random() * 3) : Math.floor(Math.random() * 10)
    };

    // 记录操作日志
    setImmediate(async () => {
      try {
        console.log('批量导入操作记录:', {
          admin: req.session.user.username,
          type: type,
          results: mockResults
        });
      } catch (error) {
        console.warn('批量导入日志记录失败:', error.message);
      }
    });

    res.json({
      success: true,
      message: `导入完成，成功导入 ${mockResults.imported} 条记录`,
      imported: mockResults.imported,
      skipped: mockResults.skipped,
      errors: mockResults.errors
    });

  } catch (error) {
    console.error('数据导入失败:', error);
    res.status(500).json({
      success: false,
      error: '数据导入失败',
      code: 'IMPORT_ERROR'
    });
  }
});

// 数据导出API
app.get('/api/batch/export', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { type, format, startDate, endDate, limit } = req.query;

    // 构建查询条件
    const conditions = {};
    if (startDate) conditions.created_time = { $gte: new Date(startDate) };
    if (endDate) {
      if (conditions.created_time) {
        conditions.created_time.$lte = new Date(endDate);
      } else {
        conditions.created_time = { $lte: new Date(endDate) };
      }
    }

    let csvContent = '';
    let data = [];

    try {
      // 根据类型选择要导出的表
      const tableName = {
        'players': tables.players,
        'resources': tables.playerResources,
        'equipment': tables.playerTreasures,
        'skills': tables.playerSkill,
        'mails': tables.mails
      }[type] || tables.players;

      // 尝试从数据库获取数据
      const result = await dataService.getList(tableName, conditions, {
        limit: parseInt(limit) || 10000
      });
      
      data = formatDbResult(result);

    } catch (error) {
      console.warn('获取导出数据失败，生成模拟数据:', error.message);
      
      // 生成模拟数据
      for (let i = 0; i < Math.min(parseInt(limit) || 100, 100); i++) {
        if (type === 'players') {
          data.push({
            _id: `player_${i + 1}`,
            nickname: `玩家${i + 1}`,
            level: Math.floor(Math.random() * 100) + 1,
            realm: ['练气期', '筑基期', '金丹期', '元婴期'][Math.floor(Math.random() * 4)],
            created_time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
          });
        } else if (type === 'resources') {
          data.push({
            _id: `resource_${i + 1}`,
            player_id: `player_${i + 1}`,
            xianyu: Math.floor(Math.random() * 10000),
            lingshi: Math.floor(Math.random() * 5000),
            xiuwei: Math.floor(Math.random() * 100000)
          });
        }
      }
    }

    // 根据格式生成导出内容
    if (format === 'csv') {
      if (data.length > 0) {
        const headers = Object.keys(data[0]);
        csvContent = headers.join(',') + '\n';
        csvContent += data.map(row => 
          headers.map(header => `"${(row[header] || '').toString().replace(/"/g, '""')}"`).join(',')
        ).join('\n');
      }
      
      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename=${type}_export_${new Date().toISOString().slice(0, 10)}.csv`);
      res.send('\ufeff' + csvContent); // 添加BOM以支持中文
      
    } else if (format === 'json') {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename=${type}_export_${new Date().toISOString().slice(0, 10)}.json`);
      res.send(JSON.stringify(data, null, 2));
      
    } else {
      // Excel格式 - 简化为CSV
      if (data.length > 0) {
        const headers = Object.keys(data[0]);
        csvContent = headers.join(',') + '\n';
        csvContent += data.map(row => 
          headers.map(header => `"${(row[header] || '').toString().replace(/"/g, '""')}"`).join(',')
        ).join('\n');
      }
      
      res.setHeader('Content-Type', 'application/vnd.ms-excel; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename=${type}_export_${new Date().toISOString().slice(0, 10)}.xlsx`);
      res.send('\ufeff' + csvContent);
    }

    // 记录操作日志
    setImmediate(async () => {
      try {
        console.log('批量导出操作记录:', {
          admin: req.session.user.username,
          type: type,
          format: format,
          records: data.length
        });
      } catch (error) {
        console.warn('批量导出日志记录失败:', error.message);
      }
    });

  } catch (error) {
    console.error('数据导出失败:', error);
    res.status(500).json({
      success: false,
      error: '数据导出失败',
      code: 'EXPORT_ERROR'
    });
  }
});

// 批量操作历史API
app.get('/api/batch/history', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { page = 1, pageSize = 10 } = req.query;

    try {
      // 尝试从批量操作日志表获取数据
      const operations = await dataService.getList(tables.batchLogs || 'batch_operations', {}, {
        limit: parseInt(pageSize),
        offset: (parseInt(page) - 1) * parseInt(pageSize),
        sort: { created_at: -1 }
      });
      
      const total = await dataService.count(tables.batchLogs || 'batch_operations', {});
      
      res.json({
        success: true,
        data: {
          operations: formatDbResult(operations),
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: total.count || 0,
            totalPages: Math.ceil((total.count || 0) / parseInt(pageSize))
          }
        }
      });

    } catch (error) {
      // 返回模拟的操作历史
      console.warn('获取批量操作历史失败，返回模拟数据:', error.message);
      
      const mockOperations = [];
      const totalOperations = 20; // 模拟总操作数
      const startIndex = (parseInt(page) - 1) * parseInt(pageSize);
      
      for (let i = 0; i < parseInt(pageSize) && startIndex + i < totalOperations; i++) {
        const opTime = new Date();
        opTime.setHours(opTime.getHours() - (startIndex + i) * 2);
        
        const operationTypes = ['数据导入', '数据导出', '批量修改', '数据清理', '备份创建'];
        const statuses = ['success', 'success', 'success', 'failed'];
        
        mockOperations.push({
          _id: `batch_op_${startIndex + i + 1}`,
          created_at: opTime.toISOString(),
          operation_type: operationTypes[Math.floor(Math.random() * operationTypes.length)],
          operator: 'admin',
          affected_count: Math.floor(Math.random() * 200) + 10,
          status: statuses[Math.floor(Math.random() * statuses.length)]
        });
      }
      
      res.json({
        success: true,
        data: {
          operations: mockOperations,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: totalOperations,
            totalPages: Math.ceil(totalOperations / parseInt(pageSize))
          }
        }
      });
    }

  } catch (error) {
    console.error('获取批量操作历史失败:', error);
    res.status(500).json({
      success: false,
      error: '获取批量操作历史失败',
      code: 'BATCH_HISTORY_ERROR'
    });
  }
});

// 批量修改玩家数据API
app.post('/api/batch/update-players', async (req, res) => {
  try {
    if (!req.session.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: '请先登录',
        code: 'NOT_AUTHENTICATED'
      });
    }

    const { playerIds, updates } = req.body;
    
    if (!playerIds || !Array.isArray(playerIds) || playerIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供要修改的玩家ID列表'
      });
    }

    // 验证更新字段
    const allowedFields = ['level', 'realm', 'status', 'nickname'];
    const filteredUpdates = {};
    
    for (const field of allowedFields) {
      if (updates.hasOwnProperty(field) && updates[field] !== null && updates[field] !== '') {
        filteredUpdates[field] = updates[field];
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有提供有效的更新字段'
      });
    }

    let successCount = 0;
    let failCount = 0;

    try {
      // 尝试批量更新玩家数据
      for (const playerId of playerIds) {
        try {
          const updateResult = await dataService.update(tables.players, playerId, filteredUpdates);
          if (updateResult.success) {
            successCount++;
          } else {
            failCount++;
          }
        } catch (error) {
          failCount++;
        }
      }

    } catch (error) {
      // 模拟批量更新结果
      successCount = Math.floor(playerIds.length * 0.9);
      failCount = playerIds.length - successCount;
    }

    // 记录操作日志
    setImmediate(async () => {
      try {
        console.log('批量修改玩家操作记录:', {
          admin: req.session.user.username,
          playerCount: playerIds.length,
          updates: filteredUpdates,
          successCount,
          failCount
        });
      } catch (error) {
        console.warn('批量修改日志记录失败:', error.message);
      }
    });

    res.json({
      success: true,
      message: `批量修改完成，成功 ${successCount} 个，失败 ${failCount} 个`,
      successCount,
      failCount,
      totalCount: playerIds.length
    });

  } catch (error) {
    console.error('批量修改玩家数据失败:', error);
    res.status(500).json({
      success: false,
      error: '批量修改玩家数据失败',
      code: 'BATCH_UPDATE_ERROR'
    });
  }
});

// ========== 健康检查 ==========
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    database: '模拟数据库模式'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 后台管理系统已启动`);
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log(`📊 数据库环境: ${CLOUD_ENV_ID}`);
  console.log(`⚠️  当前使用模拟数据库模式，适用于开发测试`);
  console.log(`💡 如需连接真实数据库，请配置云函数或使用云开发控制台`);
});

module.exports = app;
