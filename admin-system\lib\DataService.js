/**
 * DataService - 数据服务封装类
 * 提供统一的数据访问接口，支持调用不同的云函数
 */

const CloudFunctionAdapter = require('./CloudFunctionAdapter');
const config = require('../config.js');

class DataService {
  constructor(options = {}) {
    // 初始化云函数适配器
    this.adapter = new CloudFunctionAdapter({
      enableLogging: options.enableLogging !== false,
      enablePerformanceMonitoring: options.enablePerformanceMonitoring !== false,
      maxRetries: options.maxRetries || 3,
      timeout: options.timeout || 30000
    });

    // 数据表配置
    this.tables = config.tables;

    // 缓存配置
    this.enableCache = options.enableCache || false;
    this.cache = new Map();
    this.cacheTimeout = options.cacheTimeout || 300000; // 5分钟

    console.log('📊 DataService初始化完成');
  }

  /**
   * 调用云函数的通用方法
   * @param {string} functionName 云函数名称
   * @param {Object} params 请求参数
   * @returns {Promise<Object>} 云函数返回结果
   */
  async callCloudFunction(functionName, params) {
    try {
      const result = await this.adapter.callCloudFunction(functionName, params);
      return result;
    } catch (error) {
      console.error(`调用云函数 ${functionName} 失败:`, error);
      throw error;
    }
  }

  /**
   * 调用adminService云函数
   * @param {Object} params 请求参数
   * @returns {Promise<Object>} 云函数返回结果
   */
  async callAdminService(params) {
    return this.callCloudFunction('adminService', params);
  }

  /**
   * 调用databaseService云函数
   * @param {Object} params 请求参数
   * @returns {Promise<Object>} 云函数返回结果
   */
  async callDatabaseService(params) {
    return this.callCloudFunction('databaseService', params);
  }

  /**
   * 获取统计数据
   * @returns {Promise<Object>} 统计数据
   */
  async getStats() {
    try {
      console.log('📊 获取实时统计数据...');

      // 并行获取各种统计数据
      const statsPromises = [
        this.getTableCount('players'),
        this.getTableCount('skill_temp'),
        this.getTableCount('s_heart_temp'),
        this.getTableCount('treasure_tmp'),
        this.getTableCount('gacha_pools'),
        this.getTableCount('mails')
      ];

      const [
        playersCount,
        skillTemplatesCount,
        swordHeartTemplatesCount,
        treasureTemplatesCount,
        gachaPoolsCount,
        mailsCount
      ] = await Promise.allSettled(statsPromises);

      const stats = {
        players: this.getSettledValue(playersCount, 0),
        skill_templates: this.getSettledValue(skillTemplatesCount, 0),
        sword_heart_templates: this.getSettledValue(swordHeartTemplatesCount, 0),
        treasure_templates: this.getSettledValue(treasureTemplatesCount, 0),
        gacha_pools: this.getSettledValue(gachaPoolsCount, 0),
        mails: this.getSettledValue(mailsCount, 0),
        last_updated: new Date().toISOString()
      };

      return stats;

    } catch (error) {
      console.error('获取统计数据失败:', error);
      // 返回默认值而不是抛出错误
      return {
        players: 0,
        skill_templates: 0,
        sword_heart_templates: 0,
        treasure_templates: 0,
        gacha_pools: 0,
        mails: 0,
        last_updated: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * 获取表记录数量
   * @param {string} tableName 表名
   * @returns {Promise<number>} 记录数量
   */
  async getTableCount(tableName) {
    try {
      // 根据表名决定使用哪个云函数
      const isAdminTable = ['admin_users', 'admin_logs'].includes(tableName);
      const functionName = isAdminTable ? 'adminService' : 'databaseService';

      const result = await this.callCloudFunction(functionName, {
        action: 'count',
        tableName: tableName
      });

      if (result.success) {
        return result.data?.count || 0;
      } else {
        console.warn(`获取表 ${tableName} 记录数量失败:`, result.error);
        return 0;
      }
    } catch (error) {
      console.warn(`获取表 ${tableName} 记录数量异常:`, error.message);
      return 0;
    }
  }

  /**
   * 获取Promise.allSettled结果的值
   * @param {Object} settledResult Promise.allSettled的结果
   * @param {*} defaultValue 默认值
   * @returns {*} 结果值或默认值
   */
  getSettledValue(settledResult, defaultValue) {
    if (settledResult.status === 'fulfilled') {
      return settledResult.value;
    } else {
      console.warn('Promise rejected:', settledResult.reason);
      return defaultValue;
    }
  }

  /**
   * 根据表名确定目标云函数
   * @param {string} tableName 表名
   * @returns {string} 云函数名称
   */
  getTargetFunction(tableName) {
    // 管理员相关表使用adminService
    const adminTables = ['admin_users', 'admin_logs'];

    if (adminTables.includes(tableName)) {
      return 'adminService';
    }

    // 其他表使用databaseService
    return 'databaseService';
  }
}

module.exports = DataService;