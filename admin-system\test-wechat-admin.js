/**
 * 微信后台管理系统测试脚本
 * 用于测试微信登录流程、权限验证、数据操作等功能
 */

const CloudFunctionAdapter = require('./lib/CloudFunctionAdapter');
const DataService = require('./lib/DataService');

// 测试配置
const TEST_CONFIG = {
  // 测试用的openid（需要替换为实际的openid）
  testOpenid: 'test-openid-12345',
  testUsername: 'test_admin',
  testRole: 'admin'
};

class WechatAdminTester {
  constructor() {
    this.cloudAdapter = new CloudFunctionAdapter();
    this.dataService = new DataService();
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始微信后台管理系统测试...\n');
    
    try {
      // 1. 测试云函数连接
      await this.testCloudFunctionConnection();
      
      // 2. 测试adminService云函数
      await this.testAdminServiceFunction();
      
      // 3. 测试管理员权限系统
      await this.testAdminPermissionSystem();
      
      // 4. 测试数据服务适配器
      await this.testDataServiceAdapter();
      
      // 5. 测试后台管理API
      await this.testBackendAPIs();
      
      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  }

  /**
   * 测试云函数连接
   */
  async testCloudFunctionConnection() {
    console.log('📡 测试云函数连接...');
    
    try {
      // 测试databaseService连接
      const dbResult = await this.cloudAdapter.callCloudFunction('databaseService', {
        action: 'getServerTime'
      });
      
      this.addTestResult('databaseService连接', dbResult.success, dbResult.error);
      
      // 测试adminService连接（这个可能会失败，因为还没有部署）
      try {
        const adminResult = await this.cloudAdapter.callCloudFunction('adminService', {
          action: 'verifyAdmin'
        });
        
        this.addTestResult('adminService连接', adminResult.success, adminResult.error);
      } catch (error) {
        this.addTestResult('adminService连接', false, 'adminService云函数可能尚未部署');
      }
      
    } catch (error) {
      this.addTestResult('云函数连接', false, error.message);
    }
  }

  /**
   * 测试adminService云函数功能
   */
  async testAdminServiceFunction() {
    console.log('🔐 测试adminService云函数功能...');
    
    try {
      // 测试权限验证
      const verifyResult = await this.cloudAdapter.callCloudFunction('adminService', {
        action: 'verifyAdmin'
      });
      
      this.addTestResult('管理员权限验证', verifyResult !== null, verifyResult?.error);
      
      // 测试获取管理员信息
      const infoResult = await this.cloudAdapter.callCloudFunction('adminService', {
        action: 'getAdminInfo'
      });
      
      this.addTestResult('获取管理员信息', infoResult !== null, infoResult?.error);
      
    } catch (error) {
      this.addTestResult('adminService功能测试', false, error.message);
    }
  }

  /**
   * 测试管理员权限系统
   */
  async testAdminPermissionSystem() {
    console.log('👥 测试管理员权限系统...');
    
    try {
      // 测试创建管理员（模拟）
      const createAdminData = {
        openid: TEST_CONFIG.testOpenid,
        username: TEST_CONFIG.testUsername,
        role: TEST_CONFIG.testRole,
        status: 'active',
        description: '测试管理员账号'
      };
      
      // 这里只是测试数据结构，不实际创建
      const isValidAdminData = this.validateAdminData(createAdminData);
      this.addTestResult('管理员数据结构验证', isValidAdminData, '数据结构不完整');
      
      // 测试权限配置
      const permissions = this.getPermissionsByRole(TEST_CONFIG.testRole);
      this.addTestResult('权限配置获取', permissions.length > 0, '权限配置为空');
      
    } catch (error) {
      this.addTestResult('权限系统测试', false, error.message);
    }
  }

  /**
   * 测试数据服务适配器
   */
  async testDataServiceAdapter() {
    console.log('📊 测试数据服务适配器...');
    
    try {
      // 测试获取统计数据
      const stats = await this.dataService.getStats();
      this.addTestResult('统计数据获取', stats !== null, '统计数据获取失败');
      
      // 测试云函数调用方法
      const hasCallMethod = typeof this.dataService.callCloudFunction === 'function';
      this.addTestResult('云函数调用方法', hasCallMethod, '缺少callCloudFunction方法');
      
      // 测试adminService调用方法
      const hasAdminMethod = typeof this.dataService.callAdminService === 'function';
      this.addTestResult('adminService调用方法', hasAdminMethod, '缺少callAdminService方法');
      
      // 测试目标函数选择
      const targetFunction = this.dataService.getTargetFunction('admin_users');
      this.addTestResult('目标函数选择', targetFunction === 'adminService', `期望adminService，实际${targetFunction}`);
      
    } catch (error) {
      this.addTestResult('数据服务适配器测试', false, error.message);
    }
  }

  /**
   * 测试后台管理API（模拟）
   */
  async testBackendAPIs() {
    console.log('🌐 测试后台管理API（模拟）...');
    
    try {
      // 模拟session数据
      const mockSession = {
        isLoggedIn: true,
        user: {
          openid: TEST_CONFIG.testOpenid,
          username: TEST_CONFIG.testUsername,
          role: TEST_CONFIG.testRole,
          permissions: ['read', 'create', 'update', 'delete', 'manage_users'],
          loginType: 'wechat'
        }
      };
      
      // 测试session验证
      const isValidSession = this.validateSession(mockSession);
      this.addTestResult('Session验证', isValidSession, 'Session数据无效');
      
      // 测试权限检查
      const hasManagePermission = this.checkPermission(mockSession.user, 'manage_users');
      this.addTestResult('权限检查', hasManagePermission, '权限检查失败');
      
      // 测试API路由结构
      const apiRoutes = [
        '/api/admin-users',
        '/api/auth/wechat-login',
        '/api/auth/check-status'
      ];
      
      this.addTestResult('API路由结构', apiRoutes.length > 0, 'API路由配置为空');
      
    } catch (error) {
      this.addTestResult('后台API测试', false, error.message);
    }
  }

  /**
   * 验证管理员数据结构
   */
  validateAdminData(data) {
    const requiredFields = ['openid', 'username', 'role', 'status'];
    return requiredFields.every(field => data.hasOwnProperty(field) && data[field]);
  }

  /**
   * 根据角色获取权限
   */
  getPermissionsByRole(role) {
    const rolePermissions = {
      'super_admin': ['*'],
      'admin': ['read', 'create', 'update', 'delete', 'manage_users'],
      'operator': ['read', 'create', 'update'],
      'viewer': ['read']
    };
    
    return rolePermissions[role] || [];
  }

  /**
   * 验证session数据
   */
  validateSession(session) {
    return session && 
           session.isLoggedIn && 
           session.user && 
           session.user.openid && 
           session.user.username && 
           session.user.role;
  }

  /**
   * 检查用户权限
   */
  checkPermission(user, requiredPermission) {
    if (!user || !user.permissions) return false;
    
    return user.permissions.includes('*') || user.permissions.includes(requiredPermission);
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, error = null) {
    this.testResults.push({
      name: testName,
      success: success,
      error: error,
      timestamp: new Date().toISOString()
    });
    
    const status = success ? '✅' : '❌';
    const errorMsg = error ? ` (${error})` : '';
    console.log(`  ${status} ${testName}${errorMsg}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n📋 测试结果汇总:');
    console.log('=' * 50);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }
    
    console.log('\n🎯 下一步操作建议:');
    if (failedTests === 0) {
      console.log('  ✅ 所有测试通过！系统已准备就绪。');
    } else {
      console.log('  1. 部署adminService云函数到微信云开发环境');
      console.log('  2. 在云数据库中创建admin_users表');
      console.log('  3. 运行初始化脚本添加管理员账号');
      console.log('  4. 配置实际的微信openid');
      console.log('  5. 启动后台管理系统进行实际测试');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new WechatAdminTester();
  tester.runAllTests()
    .then(() => {
      console.log('\n🏁 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试失败:', error);
      process.exit(1);
    });
}

module.exports = WechatAdminTester;
