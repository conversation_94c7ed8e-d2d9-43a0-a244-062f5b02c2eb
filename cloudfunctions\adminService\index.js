// 后台管理系统专用云函数 - adminService
const cloudbase = require("@cloudbase/node-sdk")

// 指定云开发环境 ID
const app = cloudbase.init({
  env: "cloud1-9gzbxxbff827656f",
})

// 引入微信云开发SDK获取用户身份
const cloud = require('wx-server-sdk')
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

// 管理员权限配置
const ADMIN_PERMISSIONS = {
  'super_admin': ['*'], // 超级管理员拥有所有权限
  'admin': ['read', 'create', 'update', 'delete', 'manage_users'], // 普通管理员
  'operator': ['read', 'create', 'update'], // 运营人员
  'viewer': ['read'] // 只读权限
}

// 支持的数据表配置（与游戏端保持一致）
const TABLE_CONFIGS = {
  // 玩家相关数据表
  players: {
    description: '玩家基础数据',
    adminOnly: false,
    requiredPermission: 'read'
  },
  player_res: {
    description: '玩家资源数据',
    adminOnly: false,
    requiredPermission: 'read'
  },
  player_treasures: {
    description: '玩家古宝装备数据',
    adminOnly: false,
    requiredPermission: 'read'
  },
  player_skill: {
    description: '玩家技能数据',
    adminOnly: false,
    requiredPermission: 'read'
  },
  
  // 模板数据表（管理端数据）
  skill_temp: {
    description: '功法模板',
    adminOnly: true,
    requiredPermission: 'update'
  },
  treasure_tmp: {
    description: '古宝模板',
    adminOnly: true,
    requiredPermission: 'update'
  },
  s_heart_temp: {
    description: '剑心模板',
    adminOnly: true,
    requiredPermission: 'update'
  },
  item_temp: {
    description: '物品模板',
    adminOnly: true,
    requiredPermission: 'update'
  },
  treasure_set: {
    description: '套装模板',
    adminOnly: true,
    requiredPermission: 'update'
  },
  
  // 游戏系统数据表
  mails: {
    description: '邮件数据',
    adminOnly: true,
    requiredPermission: 'create'
  },
  player_mails: {
    description: '玩家邮件关系表',
    adminOnly: true,
    requiredPermission: 'read'
  },
  mail_temp: {
    description: '邮件模板表',
    adminOnly: true,
    requiredPermission: 'create'
  },
  gacha_pools: {
    description: '抽取池配置',
    adminOnly: true,
    requiredPermission: 'update'
  },
  admin_logs: {
    description: '管理员操作日志',
    adminOnly: true,
    requiredPermission: 'read'
  },
  
  // 管理员数据表
  admin_users: {
    description: '管理员用户表',
    adminOnly: true,
    requiredPermission: 'manage_users'
  }
}

/**
 * 验证管理员权限
 * @param {string} openid 用户openid
 * @param {string} requiredPermission 需要的权限
 * @returns {Promise<Object>} 权限验证结果
 */
async function verifyAdminPermission(openid, requiredPermission = 'read') {
  try {
    const models = app.models
    const adminUsersTable = models.admin_users
    
    // 查询管理员信息
    const adminQuery = await adminUsersTable.where({
      openid: openid,
      status: 'active'
    }).get()
    
    if (!adminQuery.data || adminQuery.data.length === 0) {
      return {
        success: false,
        error: '用户不是管理员或账号已被禁用',
        code: 'NOT_ADMIN'
      }
    }
    
    const adminUser = adminQuery.data[0]
    const userRole = adminUser.role || 'viewer'
    const userPermissions = ADMIN_PERMISSIONS[userRole] || ['read']
    
    // 检查权限
    const hasPermission = userPermissions.includes('*') || userPermissions.includes(requiredPermission)
    
    if (!hasPermission) {
      return {
        success: false,
        error: `权限不足，需要权限：${requiredPermission}`,
        code: 'PERMISSION_DENIED',
        userRole: userRole,
        userPermissions: userPermissions
      }
    }
    
    return {
      success: true,
      adminUser: adminUser,
      userRole: userRole,
      userPermissions: userPermissions
    }
    
  } catch (error) {
    console.error('权限验证失败:', error)
    return {
      success: false,
      error: '权限验证失败',
      code: 'PERMISSION_CHECK_ERROR'
    }
  }
}

/**
 * 记录管理员操作日志
 * @param {Object} logData 日志数据
 */
async function recordAdminLog(logData) {
  try {
    const models = app.models
    const adminLogsTable = models.admin_logs
    
    const logRecord = {
      admin_openid: logData.admin_openid,
      admin_username: logData.admin_username || '',
      action: logData.action,
      target_type: logData.target_type,
      target_id: logData.target_id || '',
      status: logData.status,
      description: logData.description,
      ip_address: logData.ip_address || '',
      user_agent: logData.user_agent || '',
      timestamp: new Date(),
      createdAt: Date.now(),
      updatedAt: Date.now()
    }
    
    await adminLogsTable.create({
      data: logRecord
    })
    
  } catch (error) {
    console.error('记录管理员日志失败:', error)
    // 日志记录失败不应该影响主要操作
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('adminService云函数被调用')
  console.log('接收到的参数:', JSON.stringify(event))
  
  // 获取用户身份
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    console.error('未获取到用户openid')
    return {
      success: false,
      error: '用户身份验证失败',
      code: 'UNAUTHORIZED'
    }
  }
  
  const { action, tableName, data = {}, conditions = {}, _id, limit = 10, skip = 0 } = event
  
  if (!action) {
    return {
      success: false,
      error: '缺少必要参数：action',
      code: 'MISSING_PARAMS'
    }
  }
  
  try {
    const models = app.models
    const currentTime = Date.now()
    
    // 处理特殊操作（不需要表名的操作）
    switch (action) {
      case 'verifyAdmin':
        return await verifyAdminPermission(openid, 'read')
      
      case 'getAdminInfo':
        const adminInfo = await verifyAdminPermission(openid, 'read')
        if (adminInfo.success) {
          return {
            success: true,
            data: {
              openid: openid,
              username: adminInfo.adminUser.username,
              role: adminInfo.userRole,
              permissions: adminInfo.userPermissions,
              created_at: adminInfo.adminUser.created_at,
              last_login: adminInfo.adminUser.last_login
            }
          }
        }
        return adminInfo
      
      case 'updateLastLogin':
        // 更新管理员最后登录时间
        const updateResult = await models.admin_users.where({
          openid: openid
        }).update({
          data: {
            last_login: new Date(),
            updatedAt: currentTime
          }
        })
        
        return {
          success: true,
          message: '登录时间更新成功'
        }
    }
    
    // 需要tableName的操作
    if (!tableName) {
      return {
        success: false,
        error: '缺少必要参数：tableName',
        code: 'MISSING_PARAMS'
      }
    }
    
    if (!TABLE_CONFIGS[tableName]) {
      return {
        success: false,
        error: `不支持的数据表：${tableName}`,
        code: 'UNSUPPORTED_TABLE'
      }
    }
    
    // 验证权限
    const tableConfig = TABLE_CONFIGS[tableName]
    const permissionCheck = await verifyAdminPermission(openid, tableConfig.requiredPermission)
    
    if (!permissionCheck.success) {
      return permissionCheck
    }
    
    // 获取数据表
    const table = models[tableName]
    let result = null
    
    // 执行数据库操作
    switch (action) {
      case 'create':
        result = await createRecord(table, tableName, data, openid, currentTime, permissionCheck)
        break
      
      case 'read':
      case 'get':
        result = await readRecords(table, tableName, conditions, _id, openid, limit, skip, permissionCheck)
        break
      
      case 'update':
        result = await updateRecord(table, tableName, data, conditions, _id, openid, currentTime, permissionCheck)
        break
      
      case 'delete':
        result = await deleteRecord(table, tableName, conditions, _id, openid, permissionCheck)
        break
      
      case 'list':
        result = await listRecords(table, tableName, openid, limit, skip, permissionCheck)
        break
      
      case 'count':
        result = await countRecords(table, tableName, conditions, openid, permissionCheck)
        break
      
      default:
        return {
          success: false,
          error: `不支持的操作：${action}`,
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
    // 记录操作日志
    await recordAdminLog({
      admin_openid: openid,
      admin_username: permissionCheck.adminUser.username,
      action: action.toUpperCase(),
      target_type: tableName,
      target_id: _id || '',
      status: result.success ? 'success' : 'failed',
      description: `${action} ${tableName} - ${result.success ? '成功' : result.error}`
    })
    
    return result
    
  } catch (error) {
    console.error('adminService云函数执行错误:', error)
    
    // 记录错误日志
    await recordAdminLog({
      admin_openid: openid,
      action: action.toUpperCase(),
      target_type: tableName || 'unknown',
      target_id: _id || '',
      status: 'error',
      description: `操作失败: ${error.message}`
    })
    
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR',
      details: error.message
    }
  }
}

// 创建记录
async function createRecord(table, tableName, data, openid, currentTime, permissionCheck) {
  console.log(`创建${tableName}表记录`)

  const completeData = {
    ...data,
    _openid: openid,
    createdAt: currentTime,
    updatedAt: currentTime,
    created_by: permissionCheck.adminUser.username
  }

  const result = await table.create({
    data: completeData
  })

  console.log('创建结果:', result)

  return {
    success: true,
    data: {
      id: result.data?.id || result.data,
      record: completeData,
      message: `${TABLE_CONFIGS[tableName].description}创建成功`
    },
    timestamp: Date.now()
  }
}

// 读取记录
async function readRecords(table, tableName, conditions, _id, openid, limit, skip, permissionCheck) {
  console.log(`读取${tableName}表记录`)

  let query = table

  if (_id) {
    // 根据ID查询单条记录
    query = query.doc(_id)
    const result = await query.get()

    return {
      success: true,
      data: result.data,
      message: `${TABLE_CONFIGS[tableName].description}查询成功`
    }
  } else {
    // 根据条件查询多条记录
    if (conditions && Object.keys(conditions).length > 0) {
      query = query.where(conditions)
    }

    const result = await query.limit(limit).skip(skip).get()

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      message: `${TABLE_CONFIGS[tableName].description}查询成功`
    }
  }
}

// 更新记录
async function updateRecord(table, tableName, data, conditions, _id, openid, currentTime, permissionCheck) {
  console.log(`更新${tableName}表记录`)

  const updateData = {
    ...data,
    updatedAt: currentTime,
    updated_by: permissionCheck.adminUser.username
  }

  let query = table

  if (_id) {
    query = query.doc(_id)
  } else if (conditions && Object.keys(conditions).length > 0) {
    query = query.where(conditions)
  } else {
    return {
      success: false,
      error: '更新操作需要指定ID或条件',
      code: 'MISSING_UPDATE_CONDITION'
    }
  }

  const result = await query.update({
    data: updateData
  })

  return {
    success: true,
    data: {
      updated: result.updated || result.stats?.updated || 1,
      message: `${TABLE_CONFIGS[tableName].description}更新成功`
    },
    timestamp: Date.now()
  }
}

// 删除记录
async function deleteRecord(table, tableName, conditions, _id, openid, permissionCheck) {
  console.log(`删除${tableName}表记录`)

  // 检查删除权限
  if (!permissionCheck.userPermissions.includes('*') && !permissionCheck.userPermissions.includes('delete')) {
    return {
      success: false,
      error: '权限不足，无法执行删除操作',
      code: 'DELETE_PERMISSION_DENIED'
    }
  }

  let query = table

  if (_id) {
    query = query.doc(_id)
  } else if (conditions && Object.keys(conditions).length > 0) {
    query = query.where(conditions)
  } else {
    return {
      success: false,
      error: '删除操作需要指定ID或条件',
      code: 'MISSING_DELETE_CONDITION'
    }
  }

  const result = await query.remove()

  return {
    success: true,
    data: {
      deleted: result.deleted || result.stats?.removed || 1,
      message: `${TABLE_CONFIGS[tableName].description}删除成功`
    },
    timestamp: Date.now()
  }
}

// 列出记录
async function listRecords(table, tableName, openid, limit, skip, permissionCheck) {
  console.log(`列出${tableName}表记录`)

  const result = await table.limit(limit).skip(skip).get()

  return {
    success: true,
    data: result.data,
    total: result.data.length,
    limit: limit,
    skip: skip,
    message: `${TABLE_CONFIGS[tableName].description}列表查询成功`
  }
}

// 统计记录数量
async function countRecords(table, tableName, conditions, openid, permissionCheck) {
  console.log(`统计${tableName}表记录数量`)

  let query = table

  if (conditions && Object.keys(conditions).length > 0) {
    query = query.where(conditions)
  }

  const result = await query.count()

  return {
    success: true,
    data: {
      count: result.total,
      message: `${TABLE_CONFIGS[tableName].description}统计成功`
    }
  }
}
